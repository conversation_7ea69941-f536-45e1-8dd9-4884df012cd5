#!/bin/bash

echo "========================================"
echo "Starting COVE Frontend with AIgency Integration"
echo "========================================"

echo ""
echo "Checking if COVE Integration Service is running..."
if curl -s http://localhost:8000/health > /dev/null 2>&1; then
    echo "✅ COVE Integration Service is running"
else
    echo "❌ COVE Integration Service is not running on localhost:8000"
    echo "Please start the integration service first:"
    echo "   cd backend/services"
    echo "   python simple_start.py"
    echo ""
    read -p "Press Enter to continue anyway or Ctrl+C to exit..."
fi

echo ""
echo "Checking Node.js and npm..."
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed or not in PATH"
    echo "Please install Node.js from https://nodejs.org/"
    exit 1
else
    echo "✅ Node.js is available ($(node --version))"
fi

if ! command -v npm &> /dev/null; then
    echo "❌ npm is not available"
    exit 1
else
    echo "✅ npm is available ($(npm --version))"
fi

echo ""
echo "Installing dependencies..."
if ! npm install; then
    echo "❌ Failed to install dependencies"
    exit 1
fi

echo ""
echo "========================================"
echo "Starting COVE Frontend..."
echo "========================================"
echo ""
echo "The frontend will be available at:"
echo "  http://localhost:3000"
echo ""
echo "Features available:"
echo "  ✅ Design System Demo"
echo "  ✅ Smart Canvas"
echo "  ✅ PAIM Assistant"
echo "  ✅ Voice Demo"
echo "  🆕 AIgency Integration Demo"
echo ""
echo "The AIgency Integration tab will show:"
echo "  - System status and health checks"
echo "  - PAIM instance management"
echo "  - Cultural adaptation testing"
echo "  - PowerOps statistics"
echo "  - Integration testing tools"
echo ""
echo "Press Ctrl+C to stop the server"
echo "========================================"

npm start
