"""
COVE Integration Startup Script
Starts the COVE-AIgency integration services
"""

import asyncio
import logging
import os
import sys
from typing import Dict, Any

import uvicorn
from fastapi import Fast<PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware

from integration.cove_integration_service import create_cove_integration_service
from cultural.adaptation_service import create_cultural_adaptation_service

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_config() -> Dict[str, Any]:
    """Load configuration from environment variables"""
    return {
        # Server configuration
        "host": os.getenv("COVE_HOST", "0.0.0.0"),
        "port": int(os.getenv("COVE_PORT", "8000")),
        
        # Service URLs
        "aigency_url": os.getenv("AIGENCY_URL", "http://localhost:3000"),
        "base_url": os.getenv("COVE_BASE_URL", "http://localhost:8000"),
        
        # COVE credentials
        "cove_email": os.getenv("COVE_EMAIL", "<EMAIL>"),
        "cove_password": os.getenv("COVE_PASSWORD", "CoveSecure123!"),
        
        # Cultural service configuration
        "cultural": {
            "redis_url": os.getenv("REDIS_URL", "redis://localhost:6379")
        },
        
        # CORS
        "allowed_origins": os.getenv("ALLOWED_ORIGINS", "*").split(","),
        
        # Logging
        "log_level": os.getenv("LOG_LEVEL", "INFO")
    }

async def create_app() -> FastAPI:
    """Create and configure the FastAPI application"""
    config = load_config()
    
    # Create main app
    app = FastAPI(
        title="COVE-AIgency Integration",
        description="Integration service between COVE and The AIgency backend",
        version="1.0.0"
    )
    
    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=config["allowed_origins"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Create services
    logger.info("Initializing COVE Integration Service...")
    cove_service = create_cove_integration_service(config)
    
    logger.info("Initializing Cultural Adaptation Service...")
    cultural_service = create_cultural_adaptation_service(config["cultural"])
    
    # Mount services
    app.mount("/cove", cove_service.get_app())
    app.mount("/cultural", cultural_service.get_app())
    
    # Add main routes
    @app.get("/")
    async def root():
        """Root endpoint"""
        return {
            "service": "COVE-AIgency Integration",
            "version": "1.0.0",
            "status": "running",
            "endpoints": {
                "cove": "/cove",
                "cultural": "/cultural",
                "health": "/health",
                "docs": "/docs"
            }
        }
    
    @app.get("/health")
    async def health_check():
        """Comprehensive health check"""
        try:
            # Check COVE service health
            import httpx
            async with httpx.AsyncClient() as client:
                cove_health = await client.get("http://localhost:8000/cove/health", timeout=5.0)
                cultural_health = await client.get("http://localhost:8000/cultural/health", timeout=5.0)
            
            return {
                "status": "healthy",
                "services": {
                    "cove_integration": cove_health.status_code == 200,
                    "cultural_adaptation": cultural_health.status_code == 200
                },
                "aigency_backend": "http://localhost:3000",
                "timestamp": "2024-12-01T00:00:00Z"
            }
        except Exception as e:
            return {
                "status": "degraded",
                "error": str(e),
                "timestamp": "2024-12-01T00:00:00Z"
            }
    
    return app

async def main():
    """Main entry point"""
    try:
        logger.info("Starting COVE-AIgency Integration Service...")
        
        # Load configuration
        config = load_config()
        
        # Create application
        app = await create_app()
        
        # Start server
        uvicorn_config = uvicorn.Config(
            app,
            host=config["host"],
            port=config["port"],
            log_level=config["log_level"].lower(),
            access_log=True
        )
        
        server = uvicorn.Server(uvicorn_config)
        
        logger.info(f"COVE Integration Service starting on {config['host']}:{config['port']}")
        logger.info("Available endpoints:")
        logger.info("  - Main API: http://localhost:8000")
        logger.info("  - COVE Integration: http://localhost:8000/cove")
        logger.info("  - Cultural Adaptation: http://localhost:8000/cultural")
        logger.info("  - Health Check: http://localhost:8000/health")
        logger.info("  - API Documentation: http://localhost:8000/docs")
        
        await server.serve()
        
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt, shutting down...")
    except Exception as e:
        logger.error(f"Server error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
