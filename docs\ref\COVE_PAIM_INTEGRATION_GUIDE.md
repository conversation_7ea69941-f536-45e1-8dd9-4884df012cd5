# 🚀 TheAIgency Backend - Cove PAIM Integration Guide

## ✅ **Phase 4 Complete - Production Ready**

**Status**: **READY FOR COVE PAIM INTEGRATION**
**Environment**: Production Docker Stack
**Last Updated**: June 1, 2025

---

## 🎯 **Quick Reference for Cove PAIM Team**

### **Primary Integration Endpoints**

| Service | URL | Purpose |
|---------|-----|---------|
| **Backend API** | `http://localhost:3000` | Main API server |
| **Health Check** | `http://localhost:3000/health` | Service status |
| **API Health** | `http://localhost:3000/api/v1/health` | API endpoints status |
| **PAIM Management** | `http://localhost:3000/api/v1/paim` | PAIM CRUD operations |
| **Authentication** | `http://localhost:3000/api/v1/auth` | User auth & tokens |
| **PowerOps** | `http://localhost:3000/api/v1/powerops` | Gamification system |

### **Database Connections**
| Service | Host | Port | Purpose |
|---------|------|------|---------|
| **PostgreSQL** | `localhost` | `5432` | Primary database |
| **Redis** | `localhost` | `6379` | Caching & sessions |
| **Qdrant** | `localhost` | `6333` | Vector database |

---

## 🔧 **Environment Setup Commands**

### **1. Start Full Production Stack**
```powershell
# Build and launch production environment
.\scripts\docker-launch.ps1 -Build -Production

# Check all services are healthy
.\scripts\docker-launch.ps1 -Health

# View real-time logs
.\scripts\docker-launch.ps1 -Logs
```

### **2. Quick Health Verification**
```bash
# Test backend API
curl http://localhost:3000/health

# Test API endpoints
curl http://localhost:3000/api/v1/health

# Test database connectivity
curl http://localhost:3000/health/db
```

---

## 🔐 **Authentication Flow for Cove PAIM**

### **Step 1: Register Cove PAIM User**
```bash
curl -X POST http://localhost:3000/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "CoveSecure123!",
    "firstName": "Cove",
    "lastName": "PAIM",
    "organizationId": "cove_org"
  }'
```

### **Step 2: Login and Get Token**
```bash
curl -X POST http://localhost:3000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "CoveSecure123!"
  }'
```

**Response includes JWT token for subsequent API calls**

---

## 🤖 **PAIM Integration Examples**

### **Create Cove PAIM Instance**
```bash
curl -X POST http://localhost:3000/api/v1/paim \
  -H "Authorization: Bearer <your_jwt_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Cove Customer Service PAIM",
    "description": "Advanced AI assistant for customer support",
    "type": "customer_service",
    "capabilities": ["chat", "email", "knowledge_base", "sentiment_analysis"],
    "configuration": {
      "model": "gpt-4",
      "temperature": 0.7,
      "maxTokens": 2048,
      "coveIntegration": true
    }
  }'
```

### **Activate PAIM for Production**
```bash
curl -X POST http://localhost:3000/api/v1/paim/{paim_id}/activate \
  -H "Authorization: Bearer <your_jwt_token>"
```

### **Get PAIM Status and Details**
```bash
curl -X GET http://localhost:3000/api/v1/paim/{paim_id} \
  -H "Authorization: Bearer <your_jwt_token>"
```

---

## 🎮 **PowerOps Gamification Integration**

### **Track Cove PAIM Performance**
```bash
# Get user performance stats
curl -X GET http://localhost:3000/api/v1/powerops/user-stats \
  -H "Authorization: Bearer <your_jwt_token>"

# Log completed tasks
curl -X POST http://localhost:3000/api/v1/powerops/complete-task \
  -H "Authorization: Bearer <your_jwt_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "taskId": "cove_customer_resolution",
    "taskType": "customer_service",
    "difficulty": "medium",
    "timeSpent": 300
  }'
```

---

## 🔧 **Agent Framework Integration**

### **Execute Cove PAIM Agent**
```bash
curl -X POST http://localhost:3000/api/v1/agents/{agent_id}/execute \
  -H "Authorization: Bearer <your_jwt_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "input": {
      "query": "Analyze customer satisfaction trends",
      "parameters": {
        "timeframe": "last_30_days",
        "source": "cove_platform"
      }
    },
    "context": {
      "userId": "cove_paim_user",
      "sessionId": "cove_session_123"
    }
  }'
```

---

## 📊 **Monitoring & Health Checks**

### **Automated Health Monitoring**
```powershell
# Continuous health monitoring
while ($true) {
    .\scripts\docker-launch.ps1 -Health
    Start-Sleep -Seconds 30
}
```

### **Service Status Indicators**
- ✅ **Backend API: Healthy** - Ready for requests
- ✅ **Database: Connected** - Data persistence active
- ✅ **Redis: Active** - Caching operational
- ✅ **Qdrant: Running** - Vector search ready

---

## 🛡️ **Security & Rate Limiting**

### **Rate Limits for Cove PAIM**
- **Authentication**: 5 requests/minute per IP
- **PAIM Operations**: 100 requests/minute per user
- **Agent Execution**: 10 requests/minute per user
- **PowerOps Updates**: 50 requests/minute per user

### **Security Features**
- JWT token authentication
- CORS protection
- Input validation and sanitization
- SQL injection prevention
- XSS protection
- Rate limiting per endpoint

---

## 🚨 **Troubleshooting Guide**

### **Common Issues & Solutions**

#### **1. Service Not Responding**
```bash
# Check Docker containers
docker ps

# Restart services
.\scripts\docker-launch.ps1 -Stop
.\scripts\docker-launch.ps1 -Production
```

#### **2. Database Connection Issues**
```bash
# Check database health
curl http://localhost:3000/health/db

# View database logs
docker-compose logs db
```

#### **3. Authentication Failures**
```bash
# Verify token format
# Ensure Bearer token is included in Authorization header
# Check token expiration (default: 24 hours)
```

#### **4. Rate Limit Exceeded**
```bash
# Wait for rate limit reset (1 minute)
# Implement exponential backoff in Cove PAIM client
# Consider upgrading rate limits for production use
```

---

## 📈 **Performance Optimization**

### **Recommended Settings for Cove PAIM**
- **Connection Pooling**: Max 20 concurrent connections
- **Request Timeout**: 30 seconds for standard operations
- **Batch Operations**: Group multiple PAIM operations when possible
- **Caching**: Utilize Redis for frequently accessed data

### **Monitoring Metrics**
- Response time: Target <200ms for API calls
- Throughput: Support 100+ requests/minute per PAIM
- Uptime: 99.9% availability target
- Error rate: <1% for production operations

---

## 🔄 **Integration Testing Checklist**

### **Pre-Integration Tests**
- [ ] Backend API health check passes
- [ ] Database connectivity confirmed
- [ ] Authentication flow working
- [ ] PAIM creation/activation successful
- [ ] PowerOps integration functional

### **Cove PAIM Integration Tests**
- [ ] Cove user registration successful
- [ ] JWT token generation working
- [ ] PAIM instance creation
- [ ] PAIM activation and status checks
- [ ] Agent execution with Cove parameters
- [ ] PowerOps task completion tracking

### **Production Readiness**
- [ ] All services healthy and stable
- [ ] Rate limiting configured appropriately
- [ ] Error handling tested
- [ ] Monitoring and logging active
- [ ] Security measures validated

---

## 📞 **Support & Contact**

**Environment Status**: ✅ **PRODUCTION READY**
**Integration Status**: ✅ **READY FOR COVE PAIM**
**Documentation**: Complete and up-to-date
**Testing**: All Phase 4 QC criteria met

**Next Steps for Cove PAIM Team**:
1. Review API endpoints and authentication flow
2. Set up test environment using provided commands
3. Implement Cove PAIM integration using reference examples
4. Conduct integration testing with provided checklist
5. Deploy to production with monitoring enabled

---

*TheAIgency Backend - Phase 4 Complete*
*Ready for Cove PAIM Integration - June 1, 2025*
