# COVE-AIgency Integration Services

This directory contains the integration services that connect COVE with The AIgency backend, providing seamless cultural adaptation and PAIM management capabilities.

## Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   COVE Frontend │    │  The AIgency    │    │ COVE Integration│
│                 │◄──►│    Backend      │◄──►│    Services     │
│   (React UI)    │    │  (Production)   │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                                               ┌─────────────────┐
                                               │   Cultural      │
                                               │  Adaptation     │
                                               │   Service       │
                                               └─────────────────┘
```

## Services

### 1. COVE Integration Service (`cove_integration_service.py`)
- Main coordination service
- Handles PAIM creation and management
- Integrates with The AIgency backend
- Manages PowerOps gamification

### 2. Cultural Adaptation Service (`cultural/adaptation_service.py`)
- Arabic dialect detection
- Cultural sensitivity adaptation
- Regional compliance
- Religious considerations

### 3. AIgency Client (`integration/aigency_client.py`)
- HTTP client for The AIgency backend
- Authentication management
- API wrapper for all AIgency endpoints

## Quick Start

### Prerequisites

1. **The AIgency Backend** running on `http://localhost:3000`
2. **Redis** running on `redis://localhost:6379`
3. **Python 3.11+**

### Installation

1. Install dependencies:
```bash
cd backend/services
pip install -r requirements.txt
```

2. Set environment variables:
```bash
export AIGENCY_URL="http://localhost:3000"
export REDIS_URL="redis://localhost:6379"
export COVE_EMAIL="<EMAIL>"
export COVE_PASSWORD="CoveSecure123!"
```

3. Start the integration service:
```bash
python start_cove_integration.py
```

### Testing the Integration

Run the integration test suite:
```bash
python test_integration.py
```

## API Endpoints

### COVE Integration Service (`/cove`)

#### Create PAIM
```http
POST /cove/api/v1/cove/paim/create
Content-Type: application/json

{
  "name": "Customer Service PAIM",
  "description": "PAIM for customer service with Arabic support",
  "cultural_region": "uae",
  "dialect_support": ["gulf_uae", "gulf_saudi"],
  "capabilities": ["chat", "email", "knowledge_base"]
}
```

#### Execute Task
```http
POST /cove/api/v1/cove/task/execute
Content-Type: application/json

{
  "task_type": "customer_service",
  "content": "I need help with my account",
  "cultural_context": {
    "region": "uae",
    "dialect": "gulf_uae",
    "formality_level": "medium",
    "religious_considerations": true
  }
}
```

#### List PAIMs
```http
GET /cove/api/v1/cove/paims
```

#### PowerOps Stats
```http
GET /cove/api/v1/cove/powerops/stats
```

### Cultural Adaptation Service (`/cultural`)

#### Detect Dialect
```http
POST /cultural/api/v1/cultural/detect-dialect
Content-Type: application/json

{
  "text": "شلونك؟ وين رايح اليوم؟",
  "context": {"region": "uae"}
}
```

#### Adapt Content
```http
POST /cultural/api/v1/cultural/adapt
Content-Type: application/json

{
  "response": {"content": "Hello, how are you?"},
  "cultural_context": {
    "region": "uae",
    "formality_level": "high",
    "religious_considerations": true
  }
}
```

## Configuration

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `AIGENCY_URL` | `http://localhost:3000` | The AIgency backend URL |
| `REDIS_URL` | `redis://localhost:6379` | Redis connection URL |
| `COVE_EMAIL` | `<EMAIL>` | COVE user email |
| `COVE_PASSWORD` | `CoveSecure123!` | COVE user password |
| `COVE_HOST` | `0.0.0.0` | Service host |
| `COVE_PORT` | `8000` | Service port |
| `LOG_LEVEL` | `INFO` | Logging level |

### Configuration File

See `config.yaml` for detailed configuration options.

## Cultural Features

### Supported Dialects
- Gulf UAE (`gulf_uae`)
- Gulf Saudi (`gulf_saudi`)
- Gulf Kuwait (`gulf_kuwait`)
- Gulf Qatar (`gulf_qatar`)
- Gulf Bahrain (`gulf_bahrain`)
- Gulf Oman (`gulf_oman`)
- Levantine (`levantine`)
- Egyptian (`egyptian`)

### Cultural Adaptations
- **Formality Levels**: Low, Medium, High
- **Religious Sensitivity**: Automatic Islamic expressions
- **Gender Considerations**: Gender-neutral language
- **Business Context**: Professional terminology
- **Regional Compliance**: Local cultural norms

## Integration with The AIgency

### Authentication Flow
1. Service starts and attempts login to The AIgency
2. If login fails, registers new COVE user
3. Stores authentication tokens for API calls
4. Automatically refreshes tokens as needed

### PAIM Management
1. Creates PAIMs with COVE-specific configuration
2. Activates PAIMs for use
3. Manages PAIM lifecycle
4. Integrates with PowerOps for gamification

### Agent Execution
1. Routes tasks to appropriate agents
2. Applies cultural adaptation to inputs
3. Executes agents through The AIgency
4. Adapts responses for cultural context
5. Logs completion for PowerOps

## Monitoring and Health Checks

### Health Endpoints
- `/health` - Overall service health
- `/cove/health` - COVE integration health
- `/cultural/health` - Cultural service health

### Monitoring
- Service connectivity monitoring
- Authentication status tracking
- PAIM instance management
- Task execution metrics
- Cultural adaptation statistics

## Troubleshooting

### Common Issues

1. **The AIgency Connection Failed**
   - Ensure The AIgency backend is running on port 3000
   - Check network connectivity
   - Verify The AIgency health endpoint

2. **Authentication Failed**
   - Check COVE credentials in environment variables
   - Verify The AIgency user registration endpoint
   - Check logs for detailed error messages

3. **Cultural Adaptation Not Working**
   - Ensure Redis is running and accessible
   - Check cultural service health endpoint
   - Verify Arabic text processing dependencies

4. **PAIM Creation Failed**
   - Ensure authentication is successful
   - Check The AIgency PAIM creation endpoint
   - Verify PAIM configuration parameters

### Logs

Logs are written to:
- Console output (structured logging)
- `logs/cove_integration.log` (if configured)

### Debug Mode

Set `LOG_LEVEL=DEBUG` for detailed logging:
```bash
export LOG_LEVEL=DEBUG
python start_cove_integration.py
```

## Development

### Adding New Features

1. **New Cultural Adaptations**
   - Add patterns to `cultural/adaptation_service.py`
   - Update dialect detection rules
   - Test with various Arabic texts

2. **New Agent Types**
   - Add agent mapping in `cove_integration_service.py`
   - Update task type routing
   - Test agent execution flow

3. **New PowerOps Features**
   - Extend PowerOps integration in `aigency_client.py`
   - Add new task types and difficulty levels
   - Update completion logging

### Testing

Run specific test categories:
```bash
# Health checks only
python -c "from test_integration import *; import asyncio; asyncio.run(COVEIntegrationTester().test_health_checks())"

# Cultural adaptation only
python -c "from test_integration import *; import asyncio; asyncio.run(COVEIntegrationTester().test_cultural_adaptation())"
```

## Production Deployment

### Docker Deployment

Use the provided `docker-compose.integration.yml`:
```bash
docker-compose -f docker-compose.integration.yml up -d
```

### Environment Setup

1. Set production environment variables
2. Configure Redis with persistence
3. Set up monitoring and alerting
4. Configure log aggregation
5. Set up backup procedures

### Security Considerations

1. Change default passwords
2. Use secure JWT secrets
3. Enable HTTPS in production
4. Implement rate limiting
5. Set up proper CORS policies

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review logs for error details
3. Test individual components
4. Verify The AIgency backend status
