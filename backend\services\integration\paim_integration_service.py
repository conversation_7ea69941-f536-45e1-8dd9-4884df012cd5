"""
PAIM Integration Service - Main coordination service
Coordinates between PAIM Gateway, Cultural Adaptation, and existing backend services
"""

import asyncio
import json
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timezone
from dataclasses import dataclass

from fastapi import FastAPI, HTTPException, BackgroundTasks
from pydantic import BaseModel, Field
import httpx
from redis import Redis

# Import our custom services
from ..api.paim_gateway import PAIMGateway, create_paim_gateway, DEFAULT_CONFIG as GATEWAY_CONFIG
from ..cultural.adaptation_service import CulturalAdaptationService, create_cultural_adaptation_service

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class IntegrationStatus(BaseModel):
    """Integration service status"""
    gateway_status: str
    cultural_service_status: str
    aigency_connection: str
    python_backend_connection: str
    redis_connection: str
    active_paims: int
    total_requests_processed: int

class PAIMIntegrationService:
    """Main PAIM Integration Service"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.app = FastAPI(title="PAIM Integration Service", version="1.0.0")
        
        # Initialize Redis client
        self.redis_client = Redis.from_url(config.get("redis_url", "redis://localhost:6379"))
        
        # Initialize sub-services
        self.gateway = create_paim_gateway(config.get("gateway", GATEWAY_CONFIG))
        self.cultural_service = create_cultural_adaptation_service(config.get("cultural", {}))
        
        # Service URLs
        self.aigency_url = config.get("aigency_url", "http://localhost:3000")
        self.python_backend_url = config.get("python_backend_url", "http://localhost:8000")
        
        # Statistics
        self.stats = {
            "requests_processed": 0,
            "cultural_adaptations": 0,
            "agent_executions": 0,
            "errors": 0
        }
        
        # Setup routes
        self._setup_routes()
        
        # Mount sub-applications
        self.app.mount("/gateway", self.gateway.get_app())
        self.app.mount("/cultural", self.cultural_service.get_app())
    
    def _setup_routes(self):
        """Setup main integration routes"""
        
        @self.app.get("/health")
        async def health_check():
            """Comprehensive health check"""
            return await self._get_integration_status()
        
        @self.app.get("/status", response_model=IntegrationStatus)
        async def get_status():
            """Get detailed integration status"""
            return await self._get_integration_status()
        
        @self.app.post("/api/v1/paim/initialize")
        async def initialize_paim(paim_config: Dict[str, Any]):
            """Initialize a new PAIM instance"""
            try:
                paim_id = paim_config.get("paim_id")
                paim_tier = paim_config.get("paim_tier")
                
                # Validate PAIM configuration
                if not paim_id or not paim_tier:
                    raise HTTPException(status_code=400, detail="Missing paim_id or paim_tier")
                
                # Initialize PAIM in the system
                result = await self._initialize_paim_instance(paim_config)
                
                return {
                    "success": True,
                    "paim_id": paim_id,
                    "initialization_result": result,
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }
                
            except Exception as e:
                logger.error(f"Error initializing PAIM: {str(e)}")
                raise HTTPException(status_code=500, detail=f"PAIM initialization failed: {str(e)}")
        
        @self.app.post("/api/v1/paim/execute")
        async def execute_paim_task(
            task_request: Dict[str, Any],
            background_tasks: BackgroundTasks
        ):
            """Execute PAIM task with full integration"""
            try:
                # Extract task information
                paim_id = task_request.get("paim_id")
                task_type = task_request.get("task_type")
                task_data = task_request.get("task_data", {})
                cultural_context = task_request.get("cultural_context", {})
                
                # Validate request
                if not paim_id or not task_type:
                    raise HTTPException(status_code=400, detail="Missing paim_id or task_type")
                
                # Execute task through appropriate service
                result = await self._execute_integrated_task(
                    paim_id, task_type, task_data, cultural_context
                )
                
                # Update statistics in background
                background_tasks.add_task(self._update_statistics, "task_executed")
                
                return result
                
            except Exception as e:
                logger.error(f"Error executing PAIM task: {str(e)}")
                self.stats["errors"] += 1
                raise HTTPException(status_code=500, detail=f"Task execution failed: {str(e)}")
        
        @self.app.get("/api/v1/paim/{paim_id}/status")
        async def get_paim_status(paim_id: str):
            """Get status of specific PAIM instance"""
            try:
                status = await self._get_paim_status(paim_id)
                return status
                
            except Exception as e:
                logger.error(f"Error getting PAIM status: {str(e)}")
                raise HTTPException(status_code=500, detail=f"Status retrieval failed: {str(e)}")
        
        @self.app.post("/api/v1/integration/sync")
        async def sync_services():
            """Synchronize all integration services"""
            try:
                sync_result = await self._sync_all_services()
                return {
                    "success": True,
                    "sync_result": sync_result,
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }
                
            except Exception as e:
                logger.error(f"Error syncing services: {str(e)}")
                raise HTTPException(status_code=500, detail=f"Service sync failed: {str(e)}")
        
        @self.app.get("/api/v1/integration/metrics")
        async def get_metrics():
            """Get integration metrics and statistics"""
            try:
                metrics = await self._get_comprehensive_metrics()
                return metrics
                
            except Exception as e:
                logger.error(f"Error getting metrics: {str(e)}")
                raise HTTPException(status_code=500, detail=f"Metrics retrieval failed: {str(e)}")
    
    async def _get_integration_status(self) -> IntegrationStatus:
        """Get comprehensive integration status"""
        try:
            # Check gateway status
            gateway_status = "healthy"
            try:
                # Test gateway health
                async with httpx.AsyncClient() as client:
                    response = await client.get(f"{self.config.get('base_url', 'http://localhost:8000')}/gateway/health")
                    if response.status_code != 200:
                        gateway_status = "unhealthy"
            except:
                gateway_status = "unreachable"
            
            # Check cultural service status
            cultural_status = "healthy"
            try:
                async with httpx.AsyncClient() as client:
                    response = await client.get(f"{self.config.get('base_url', 'http://localhost:8000')}/cultural/health")
                    if response.status_code != 200:
                        cultural_status = "unhealthy"
            except:
                cultural_status = "unreachable"
            
            # Check AIgency connection
            aigency_status = "connected"
            try:
                async with httpx.AsyncClient() as client:
                    response = await client.get(f"{self.aigency_url}/health", timeout=5.0)
                    if response.status_code != 200:
                        aigency_status = "unhealthy"
            except:
                aigency_status = "unreachable"
            
            # Check Python backend connection
            backend_status = "connected"
            try:
                async with httpx.AsyncClient() as client:
                    response = await client.get(f"{self.python_backend_url}/health", timeout=5.0)
                    if response.status_code != 200:
                        backend_status = "unhealthy"
            except:
                backend_status = "unreachable"
            
            # Check Redis connection
            redis_status = "connected"
            try:
                self.redis_client.ping()
            except:
                redis_status = "unreachable"
            
            # Get active PAIMs count
            active_paims = len(self.gateway.active_connections)
            
            return IntegrationStatus(
                gateway_status=gateway_status,
                cultural_service_status=cultural_status,
                aigency_connection=aigency_status,
                python_backend_connection=backend_status,
                redis_connection=redis_status,
                active_paims=active_paims,
                total_requests_processed=self.stats["requests_processed"]
            )
            
        except Exception as e:
            logger.error(f"Error getting integration status: {str(e)}")
            raise
    
    async def _initialize_paim_instance(self, paim_config: Dict[str, Any]) -> Dict[str, Any]:
        """Initialize a new PAIM instance"""
        paim_id = paim_config["paim_id"]
        paim_tier = paim_config["paim_tier"]
        
        # Store PAIM configuration
        config_key = f"paim_config:{paim_id}"
        self.redis_client.setex(config_key, 86400, json.dumps(paim_config))  # 24 hours TTL
        
        # Initialize cultural preferences
        cultural_prefs = paim_config.get("cultural_preferences", {})
        if cultural_prefs:
            prefs_key = f"cultural_preferences:{paim_id}"
            self.redis_client.setex(prefs_key, 604800, json.dumps(cultural_prefs))  # 7 days TTL
        
        # Register with AIgency if it's an agent-capable PAIM
        if paim_tier in ["COMPANY_ADMIN", "POWER_USER"]:
            try:
                async with httpx.AsyncClient() as client:
                    response = await client.post(
                        f"{self.aigency_url}/api/v1/agents/register",
                        json={
                            "paim_id": paim_id,
                            "paim_tier": paim_tier,
                            "capabilities": paim_config.get("capabilities", [])
                        },
                        timeout=10.0
                    )
                    
                    if response.status_code != 200:
                        logger.warning(f"Failed to register PAIM {paim_id} with AIgency")
            except Exception as e:
                logger.warning(f"Could not register PAIM {paim_id} with AIgency: {str(e)}")
        
        return {
            "paim_id": paim_id,
            "tier": paim_tier,
            "status": "initialized",
            "cultural_preferences_set": bool(cultural_prefs),
            "aigency_registered": paim_tier in ["COMPANY_ADMIN", "POWER_USER"]
        }

    async def _execute_integrated_task(
        self,
        paim_id: str,
        task_type: str,
        task_data: Dict[str, Any],
        cultural_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Execute task with full integration across services"""

        # Route task based on type
        if task_type.startswith("agent_"):
            # Route to AIgency
            result = await self._execute_agent_task(paim_id, task_type, task_data, cultural_context)
        elif task_type.startswith("cultural_"):
            # Route to cultural service
            result = await self._execute_cultural_task(paim_id, task_type, task_data, cultural_context)
        elif task_type.startswith("backend_"):
            # Route to Python backend
            result = await self._execute_backend_task(paim_id, task_type, task_data, cultural_context)
        else:
            # Default routing through gateway
            result = await self._execute_gateway_task(paim_id, task_type, task_data, cultural_context)

        # Apply cultural adaptation if needed
        if cultural_context and result.get("success"):
            result = await self._apply_cultural_adaptation(result, cultural_context)

        # Update statistics
        self.stats["requests_processed"] += 1

        return result

    async def _execute_agent_task(
        self,
        paim_id: str,
        task_type: str,
        task_data: Dict[str, Any],
        cultural_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Execute agent-related task through AIgency"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.aigency_url}/api/v1/agents/execute",
                    json={
                        "paim_id": paim_id,
                        "task_type": task_type,
                        "task_data": task_data,
                        "cultural_context": cultural_context
                    },
                    headers={
                        "X-PAIM-ID": paim_id,
                        "X-CULTURAL-REGION": cultural_context.get("region", "uae")
                    },
                    timeout=30.0
                )

                if response.status_code == 200:
                    self.stats["agent_executions"] += 1
                    return {
                        "success": True,
                        "data": response.json(),
                        "service": "aigency",
                        "task_type": task_type
                    }
                else:
                    return {
                        "success": False,
                        "error": f"AIgency error: {response.status_code}",
                        "service": "aigency"
                    }

        except Exception as e:
            logger.error(f"Error executing agent task: {str(e)}")
            return {
                "success": False,
                "error": f"Agent task execution failed: {str(e)}",
                "service": "aigency"
            }

    async def _execute_cultural_task(
        self,
        paim_id: str,
        task_type: str,
        task_data: Dict[str, Any],
        cultural_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Execute cultural adaptation task"""
        try:
            if task_type == "cultural_adapt":
                async with httpx.AsyncClient() as client:
                    response = await client.post(
                        f"{self.config.get('base_url', 'http://localhost:8000')}/cultural/api/v1/cultural/adapt",
                        json={
                            "response": task_data,
                            "cultural_context": cultural_context,
                            "paim_id": paim_id
                        },
                        timeout=15.0
                    )

                    if response.status_code == 200:
                        self.stats["cultural_adaptations"] += 1
                        return {
                            "success": True,
                            "data": response.json(),
                            "service": "cultural_adaptation",
                            "task_type": task_type
                        }
                    else:
                        return {
                            "success": False,
                            "error": f"Cultural service error: {response.status_code}",
                            "service": "cultural_adaptation"
                        }

            elif task_type == "cultural_detect_dialect":
                async with httpx.AsyncClient() as client:
                    response = await client.post(
                        f"{self.config.get('base_url', 'http://localhost:8000')}/cultural/api/v1/cultural/detect-dialect",
                        json={
                            "text": task_data.get("text", ""),
                            "context": cultural_context
                        },
                        timeout=10.0
                    )

                    if response.status_code == 200:
                        return {
                            "success": True,
                            "data": response.json(),
                            "service": "cultural_adaptation",
                            "task_type": task_type
                        }
                    else:
                        return {
                            "success": False,
                            "error": f"Dialect detection error: {response.status_code}",
                            "service": "cultural_adaptation"
                        }

            else:
                return {
                    "success": False,
                    "error": f"Unknown cultural task type: {task_type}",
                    "service": "cultural_adaptation"
                }

        except Exception as e:
            logger.error(f"Error executing cultural task: {str(e)}")
            return {
                "success": False,
                "error": f"Cultural task execution failed: {str(e)}",
                "service": "cultural_adaptation"
            }

    async def _execute_backend_task(
        self,
        paim_id: str,
        task_type: str,
        task_data: Dict[str, Any],
        cultural_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Execute backend task through Python backend"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.python_backend_url}/api/v1/tasks/execute",
                    json={
                        "paim_id": paim_id,
                        "task_type": task_type,
                        "task_data": task_data,
                        "cultural_context": cultural_context
                    },
                    headers={
                        "X-PAIM-ID": paim_id,
                        "X-CULTURAL-REGION": cultural_context.get("region", "uae")
                    },
                    timeout=30.0
                )

                if response.status_code == 200:
                    return {
                        "success": True,
                        "data": response.json(),
                        "service": "python_backend",
                        "task_type": task_type
                    }
                else:
                    return {
                        "success": False,
                        "error": f"Backend error: {response.status_code}",
                        "service": "python_backend"
                    }

        except Exception as e:
            logger.error(f"Error executing backend task: {str(e)}")
            return {
                "success": False,
                "error": f"Backend task execution failed: {str(e)}",
                "service": "python_backend"
            }

    async def _execute_gateway_task(
        self,
        paim_id: str,
        task_type: str,
        task_data: Dict[str, Any],
        cultural_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Execute task through PAIM gateway (default routing)"""
        try:
            # Route through gateway for general tasks
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.config.get('base_url', 'http://localhost:8000')}/gateway/api/v1/paim/execute",
                    json={
                        "method": "POST",
                        "path": f"/api/v1/tasks/{task_type}",
                        "body": task_data,
                        "cultural_context": cultural_context,
                        "paim_context": {"paim_id": paim_id}
                    },
                    headers={
                        "Authorization": f"Bearer {self._generate_internal_token(paim_id)}",
                        "X-PAIM-ID": paim_id
                    },
                    timeout=30.0
                )

                if response.status_code == 200:
                    return {
                        "success": True,
                        "data": response.json(),
                        "service": "gateway",
                        "task_type": task_type
                    }
                else:
                    return {
                        "success": False,
                        "error": f"Gateway error: {response.status_code}",
                        "service": "gateway"
                    }

        except Exception as e:
            logger.error(f"Error executing gateway task: {str(e)}")
            return {
                "success": False,
                "error": f"Gateway task execution failed: {str(e)}",
                "service": "gateway"
            }

    async def _apply_cultural_adaptation(self, result: Dict[str, Any], cultural_context: Dict[str, Any]) -> Dict[str, Any]:
        """Apply cultural adaptation to task result"""
        try:
            if not result.get("data"):
                return result

            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.config.get('base_url', 'http://localhost:8000')}/cultural/api/v1/cultural/adapt",
                    json={
                        "response": result["data"],
                        "cultural_context": cultural_context
                    },
                    timeout=10.0
                )

                if response.status_code == 200:
                    adaptation_result = response.json()
                    if adaptation_result.get("adapted_content"):
                        result["data"] = adaptation_result["adapted_content"]
                        result["cultural_adaptation"] = adaptation_result.get("adaptation_metadata", {})
                        self.stats["cultural_adaptations"] += 1

        except Exception as e:
            logger.warning(f"Cultural adaptation failed: {str(e)}")
            # Continue without adaptation rather than failing

        return result

    def _generate_internal_token(self, paim_id: str) -> str:
        """Generate internal JWT token for service communication"""
        import jwt
        from datetime import timedelta

        payload = {
            "paim_id": paim_id,
            "paim_tier": "SYSTEM_ADMIN",  # Internal service token
            "tenant_id": "system",
            "user_id": "system",
            "cultural_region": "uae",
            "permissions": ["*"],
            "session_id": f"internal_{paim_id}",
            "exp": datetime.now(timezone.utc) + timedelta(minutes=5)
        }

        return jwt.encode(payload, self.config.get("jwt_secret", "default-secret"), algorithm="HS256")

    async def _get_paim_status(self, paim_id: str) -> Dict[str, Any]:
        """Get status of specific PAIM instance"""
        try:
            # Get PAIM configuration
            config_key = f"paim_config:{paim_id}"
            config_data = self.redis_client.get(config_key)

            if not config_data:
                return {
                    "paim_id": paim_id,
                    "status": "not_found",
                    "error": "PAIM configuration not found"
                }

            config = json.loads(config_data)

            # Check if PAIM is connected
            is_connected = paim_id in self.gateway.active_connections

            # Get recent activity
            recent_key = f"recent_activity:{paim_id}"
            recent_activities = self.redis_client.lrange(recent_key, 0, 4)
            activities = [json.loads(activity) for activity in recent_activities]

            return {
                "paim_id": paim_id,
                "status": "active" if is_connected else "inactive",
                "tier": config.get("paim_tier"),
                "connected": is_connected,
                "last_activity": activities[0] if activities else None,
                "recent_activities": len(activities),
                "cultural_region": config.get("cultural_preferences", {}).get("region", "uae"),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

        except Exception as e:
            logger.error(f"Error getting PAIM status: {str(e)}")
            return {
                "paim_id": paim_id,
                "status": "error",
                "error": str(e)
            }

    async def _sync_all_services(self) -> Dict[str, Any]:
        """Synchronize all integration services"""
        sync_results = {}

        # Sync with AIgency
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(f"{self.aigency_url}/api/v1/sync", timeout=10.0)
                sync_results["aigency"] = {
                    "status": "success" if response.status_code == 200 else "failed",
                    "status_code": response.status_code
                }
        except Exception as e:
            sync_results["aigency"] = {"status": "error", "error": str(e)}

        # Sync with Python backend
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(f"{self.python_backend_url}/api/v1/sync", timeout=10.0)
                sync_results["python_backend"] = {
                    "status": "success" if response.status_code == 200 else "failed",
                    "status_code": response.status_code
                }
        except Exception as e:
            sync_results["python_backend"] = {"status": "error", "error": str(e)}

        # Sync Redis data
        try:
            # Clean up expired keys
            self.redis_client.eval("""
                local keys = redis.call('keys', ARGV[1])
                for i=1,#keys do
                    local ttl = redis.call('ttl', keys[i])
                    if ttl == -1 then
                        redis.call('expire', keys[i], 86400)
                    end
                end
                return #keys
            """, 0, "paim_*")

            sync_results["redis"] = {"status": "success", "message": "Cleanup completed"}
        except Exception as e:
            sync_results["redis"] = {"status": "error", "error": str(e)}

        return sync_results

    async def _get_comprehensive_metrics(self) -> Dict[str, Any]:
        """Get comprehensive integration metrics"""
        try:
            # Basic statistics
            metrics = {
                "integration_stats": self.stats.copy(),
                "active_connections": len(self.gateway.active_connections),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

            # Redis metrics
            try:
                redis_info = self.redis_client.info()
                metrics["redis_stats"] = {
                    "connected_clients": redis_info.get("connected_clients", 0),
                    "used_memory": redis_info.get("used_memory_human", "0B"),
                    "total_commands_processed": redis_info.get("total_commands_processed", 0)
                }
            except:
                metrics["redis_stats"] = {"status": "unavailable"}

            # Service health metrics
            metrics["service_health"] = await self._get_integration_status()

            return metrics

        except Exception as e:
            logger.error(f"Error getting comprehensive metrics: {str(e)}")
            return {
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

    async def _update_statistics(self, stat_type: str):
        """Update statistics in background"""
        try:
            if stat_type == "task_executed":
                # Store daily statistics
                today = datetime.now(timezone.utc).strftime("%Y-%m-%d")
                daily_key = f"daily_stats:{today}"
                self.redis_client.hincrby(daily_key, "tasks_executed", 1)
                self.redis_client.expire(daily_key, 604800)  # 7 days TTL

        except Exception as e:
            logger.error(f"Error updating statistics: {str(e)}")

    def get_app(self) -> FastAPI:
        """Get the FastAPI application instance"""
        return self.app


# Factory function to create integration service
def create_paim_integration_service(config: Dict[str, Any]) -> PAIMIntegrationService:
    """Create and configure PAIM Integration Service instance"""
    return PAIMIntegrationService(config)
