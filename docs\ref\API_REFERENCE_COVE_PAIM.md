# TheAIgency Backend API Reference for Cove PAIM Team

## 🚀 **Production Environment Details**

**Base URL**: `http://localhost:3000`
**Environment**: Production Docker Stack
**Status**: ✅ **READY FOR INTEGRATION**

---

## 📋 **Core Service Endpoints**

### **Health & Status**
```
GET /health
GET /health/db
GET /api/v1/health
```

### **Authentication**
```
POST /api/v1/auth/register
POST /api/v1/auth/login
POST /api/v1/auth/logout
POST /api/v1/auth/refresh
GET  /api/v1/auth/profile
```

### **PAIM Management**
```
GET    /api/v1/paim
POST   /api/v1/paim
GET    /api/v1/paim/:id
PUT    /api/v1/paim/:id
DELETE /api/v1/paim/:id
POST   /api/v1/paim/:id/activate
POST   /api/v1/paim/:id/deactivate
```

### **PowerOps Gamification**
```
GET    /api/v1/powerops/leaderboard
GET    /api/v1/powerops/achievements
POST   /api/v1/powerops/complete-task
GET    /api/v1/powerops/user-stats
POST   /api/v1/powerops/redeem-reward
```

### **Agent Framework**
```
GET    /api/v1/agents
POST   /api/v1/agents
GET    /api/v1/agents/:id
PUT    /api/v1/agents/:id
DELETE /api/v1/agents/:id
POST   /api/v1/agents/:id/execute
```

### **Workflow Collaboration**
```
GET    /api/v1/workflows
POST   /api/v1/workflows
GET    /api/v1/workflows/:id
PUT    /api/v1/workflows/:id
DELETE /api/v1/workflows/:id
POST   /api/v1/workflows/:id/assign
POST   /api/v1/workflows/:id/complete
```

---

## 🔐 **Authentication Flow**

### **1. User Registration**
```http
POST /api/v1/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "firstName": "John",
  "lastName": "Doe",
  "organizationId": "org_123"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user_123",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "refresh_token_here"
  }
}
```

### **2. User Login**
```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "SecurePassword123!"
}
```

### **3. Token Refresh**
```http
POST /api/v1/auth/refresh
Authorization: Bearer <refresh_token>
```

---

## 🤖 **PAIM Integration Endpoints**

### **Create PAIM**
```http
POST /api/v1/paim
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "Customer Service PAIM",
  "description": "Handles customer inquiries and support",
  "type": "customer_service",
  "capabilities": ["chat", "email", "knowledge_base"],
  "configuration": {
    "model": "gpt-4",
    "temperature": 0.7,
    "maxTokens": 2048
  }
}
```

### **Get PAIM Details**
```http
GET /api/v1/paim/:id
Authorization: Bearer <token>
```

### **Activate PAIM**
```http
POST /api/v1/paim/:id/activate
Authorization: Bearer <token>
```

---

## 🎮 **PowerOps Integration**

### **Get User Stats**
```http
GET /api/v1/powerops/user-stats
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "level": 5,
    "experience": 2450,
    "points": 1200,
    "achievements": 12,
    "tasksCompleted": 45,
    "streak": 7
  }
}
```

### **Complete Task**
```http
POST /api/v1/powerops/complete-task
Authorization: Bearer <token>
Content-Type: application/json

{
  "taskId": "task_123",
  "taskType": "paim_creation",
  "difficulty": "medium",
  "timeSpent": 1800
}
```

---

## 🔧 **Agent Framework Integration**

### **Execute Agent**
```http
POST /api/v1/agents/:id/execute
Authorization: Bearer <token>
Content-Type: application/json

{
  "input": {
    "query": "Analyze customer sentiment from recent reviews",
    "parameters": {
      "timeframe": "last_30_days",
      "source": "all_channels"
    }
  },
  "context": {
    "userId": "user_123",
    "sessionId": "session_456"
  }
}
```

---

## 📊 **Data Models**

### **User Model**
```typescript
interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  organizationId: string;
  role: 'admin' | 'user' | 'viewer';
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}
```

### **PAIM Model**
```typescript
interface PAIM {
  id: string;
  name: string;
  description: string;
  type: string;
  ownerId: string;
  organizationId: string;
  isActive: boolean;
  capabilities: string[];
  configuration: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}
```

### **PowerOps Stats Model**
```typescript
interface PowerOpsStats {
  userId: string;
  level: number;
  experience: number;
  points: number;
  achievements: number;
  tasksCompleted: number;
  streak: number;
  lastActivity: string;
}
```

---

## 🌐 **Environment Configuration**

### **Docker Services**
- **Backend API**: `http://localhost:3000`
- **Database**: `localhost:5432` (PostgreSQL)
- **Redis Cache**: `localhost:6379`
- **Vector DB**: `http://localhost:6333` (Qdrant)

### **Health Check Commands**
```powershell
# Check all services
.\scripts\docker-launch.ps1 -Health

# Check specific endpoints
curl http://localhost:3000/health
curl http://localhost:3000/api/v1/health
```

---

## 🔒 **Security & Rate Limiting**

### **Rate Limits**
- **Authentication**: 5 requests/minute per IP
- **API Endpoints**: 100 requests/minute per user
- **Agent Execution**: 10 requests/minute per user

### **Security Headers**
- CORS enabled for specified origins
- Helmet.js security middleware
- JWT token validation
- Input validation and sanitization

---

## 📝 **Error Handling**

### **Standard Error Response**
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input parameters",
    "details": {
      "field": "email",
      "issue": "Invalid email format"
    }
  }
}
```

### **Common Error Codes**
- `AUTHENTICATION_REQUIRED` (401)
- `AUTHORIZATION_FAILED` (403)
- `VALIDATION_ERROR` (400)
- `RESOURCE_NOT_FOUND` (404)
- `RATE_LIMIT_EXCEEDED` (429)
- `INTERNAL_SERVER_ERROR` (500)

---

## 🚀 **Quick Start for Cove PAIM Integration**

### **1. Environment Setup**
```bash
# Clone and start the backend
git clone <repository>
cd backend
.\scripts\docker-launch.ps1 -Build -Production
```

### **2. Test Connection**
```bash
curl http://localhost:3000/health
```

### **3. Create Test User**
```bash
curl -X POST http://localhost:3000/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"TestPass123!","firstName":"Test","lastName":"User"}'
```

### **4. Create Test PAIM**
```bash
curl -X POST http://localhost:3000/api/v1/paim \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{"name":"Cove Test PAIM","type":"test","capabilities":["chat"]}'
```

---

## 📞 **Support & Documentation**

**API Documentation**: Available at `/api/v1/docs` (when running)
**Health Dashboard**: Available at `/health`
**Logs**: `.\scripts\docker-launch.ps1 -Logs`

**Status**: ✅ **PRODUCTION READY FOR COVE PAIM INTEGRATION**

---

*Generated: June 1, 2025 - Phase 4 Completion*

# 1. Register Cove PAIM user
curl -X POST http://localhost:3000/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"CoveSecure123!","firstName":"Cove","lastName":"PAIM"}'

# 2. Create Cove PAIM instance
curl -X POST http://localhost:3000/api/v1/paim \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{"name":"Cove Customer Service PAIM","type":"customer_service","capabilities":["chat","email"]}'

# 3. Activate PAIM for production
curl -X POST http://localhost:3000/api/v1/paim/{paim_id}/activate \
  -H "Authorization: Bearer <token>"