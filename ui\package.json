{"name": "cove-smart-canvas-ui", "version": "1.0.0", "description": "SMART Canvas UI for Cove - AI-integrated strategic planning interface", "main": "src/index.js", "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "dev": "react-scripts start", "serve": "serve -s build -l 3505"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "zustand": "^4.4.1", "immer": "^10.0.2", "axios": "^1.5.0", "react-router-dom": "^6.15.0", "i18next": "^23.4.4", "react-i18next": "^13.2.2", "i18next-browser-languagedetector": "^7.1.0", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "styled-components": "^6.0.7", "tailwindcss": "^3.3.3", "@tailwindcss/typography": "^0.5.9", "autoprefixer": "^10.4.15", "postcss": "^8.4.29", "clsx": "^2.0.0", "focus-trap-react": "^10.2.3", "@radix-ui/react-accessible-icon": "^1.0.3", "@radix-ui/react-focus-scope": "^1.0.4", "@radix-ui/react-id": "^1.0.1", "@radix-ui/react-visually-hidden": "^1.0.3", "react-aria": "^3.28.0", "react-stately": "^3.26.0"}, "devDependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.4.3", "serve": "^14.2.1", "@storybook/addon-essentials": "^7.4.0", "@storybook/addon-interactions": "^7.4.0", "@storybook/addon-links": "^7.4.0", "@storybook/addon-onboarding": "^1.0.8", "@storybook/blocks": "^7.4.0", "@storybook/react": "^7.4.0", "@storybook/react-vite": "^7.4.0", "@storybook/testing-library": "^0.2.0", "storybook": "^7.4.0", "@axe-core/react": "^4.7.3", "jest-axe": "^8.0.0", "typescript": "^4.9.5", "@types/react": "^18.2.22", "@types/react-dom": "^18.2.7", "@types/jest": "^29.5.5"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:8000"}