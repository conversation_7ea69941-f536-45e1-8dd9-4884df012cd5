/**
 * AIgency Integration Service for COVE Frontend
 * Connects COVE UI with The AIgency backend through our integration service
 */

import axios from 'axios';

class AIgencyIntegrationService {
  constructor(baseURL = 'http://localhost:8000') {
    this.baseURL = baseURL;
    this.client = axios.create({
      baseURL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Add request interceptor for logging
    this.client.interceptors.request.use(
      (config) => {
        console.log(`[AIgency] ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('[AIgency] Request error:', error);
        return Promise.reject(error);
      }
    );

    // Add response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => {
        console.log(`[AIgency] Response: ${response.status} ${response.config.url}`);
        return response;
      },
      (error) => {
        console.error('[AIgency] Response error:', error.response?.data || error.message);
        return Promise.reject(error);
      }
    );
  }

  // Health Check Methods
  async checkHealth() {
    try {
      const response = await this.client.get('/health');
      return {
        success: true,
        data: response.data,
        status: response.data.status
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        status: 'unhealthy'
      };
    }
  }

  async checkCOVEIntegrationHealth() {
    try {
      const response = await this.client.get('/cove/health');
      return {
        success: true,
        data: response.data,
        isAuthenticated: response.data.cove_integration === 'active'
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        isAuthenticated: false
      };
    }
  }

  // PAIM Management Methods
  async createPAIM(paimConfig) {
    try {
      const response = await this.client.post('/cove/api/v1/cove/paim/create', {
        name: paimConfig.name || 'COVE PAIM',
        description: paimConfig.description || 'COVE PAIM with cultural adaptation',
        cultural_region: paimConfig.culturalRegion || 'uae',
        dialect_support: paimConfig.dialectSupport || ['gulf_uae'],
        capabilities: paimConfig.capabilities || ['chat', 'email', 'cultural_adaptation']
      });

      if (response.data.success) {
        return {
          success: true,
          data: {
            paimId: response.data.paim_id,
            status: response.data.status,
            culturalFeatures: response.data.cove_features
          }
        };
      } else {
        return {
          success: false,
          error: response.data.error
        };
      }
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.detail || error.message
      };
    }
  }

  async listPAIMs() {
    try {
      const response = await this.client.get('/cove/api/v1/cove/paims');
      
      if (response.data.success) {
        return {
          success: true,
          data: response.data.paims,
          total: response.data.total
        };
      } else {
        return {
          success: false,
          error: response.data.error
        };
      }
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.detail || error.message
      };
    }
  }

  // Task Execution Methods
  async executeTask(taskConfig) {
    try {
      const response = await this.client.post('/cove/api/v1/cove/task/execute', {
        task_type: taskConfig.taskType || 'customer_service',
        content: taskConfig.content,
        cultural_context: {
          region: taskConfig.culturalContext?.region || 'uae',
          dialect: taskConfig.culturalContext?.dialect || 'gulf_uae',
          formality_level: taskConfig.culturalContext?.formalityLevel || 'medium',
          religious_considerations: taskConfig.culturalContext?.religiousConsiderations || true,
          business_context: taskConfig.culturalContext?.businessContext || false
        },
        user_preferences: taskConfig.userPreferences || {}
      });

      if (response.data.success) {
        return {
          success: true,
          data: {
            result: response.data.result,
            agentId: response.data.agent_id,
            culturalAdaptationsApplied: response.data.cultural_adaptations_applied,
            taskType: response.data.task_type
          }
        };
      } else {
        return {
          success: false,
          error: response.data.error
        };
      }
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.detail || error.message
      };
    }
  }

  // Cultural Adaptation Methods
  async detectDialect(text, context = {}) {
    try {
      const response = await this.client.post('/cultural/api/v1/cultural/detect-dialect', {
        text,
        context: {
          region: context.region || 'uae',
          ...context
        }
      });

      return {
        success: true,
        data: {
          detectedDialect: response.data.detected_dialect,
          confidence: response.data.confidence,
          alternativeDialects: response.data.alternative_dialects,
          linguisticFeatures: response.data.linguistic_features
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.detail || error.message
      };
    }
  }

  async adaptContent(content, culturalContext) {
    try {
      const response = await this.client.post('/cultural/api/v1/cultural/adapt', {
        response: { content },
        cultural_context: {
          region: culturalContext.region || 'uae',
          formality_level: culturalContext.formalityLevel || 'medium',
          religious_considerations: culturalContext.religiousConsiderations || true,
          business_context: culturalContext.businessContext || false,
          dialect: culturalContext.dialect || 'gulf_uae'
        }
      });

      return {
        success: true,
        data: {
          adaptedContent: response.data.adapted_content,
          adaptationMetadata: response.data.adaptation_metadata,
          confidenceScore: response.data.confidence_score,
          culturalAppropriatenessScore: response.data.cultural_appropriateness_score
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.detail || error.message
      };
    }
  }

  // PowerOps Integration Methods
  async getPowerOpsStats() {
    try {
      const response = await this.client.get('/cove/api/v1/cove/powerops/stats');
      
      if (response.data.success) {
        return {
          success: true,
          data: {
            stats: response.data.stats,
            coveEnhancements: response.data.cove_enhancements
          }
        };
      } else {
        return {
          success: false,
          error: response.data.error
        };
      }
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.detail || error.message
      };
    }
  }

  // Test Methods
  async testAIgencyConnection() {
    try {
      const response = await this.client.post('/api/v1/test/aigency');
      return {
        success: true,
        data: {
          aigencyStatus: response.data.aigency_status,
          aigencyUrl: response.data.aigency_url,
          message: response.data.message
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.detail || error.message
      };
    }
  }

  async testCulturalFeatures() {
    try {
      const response = await this.client.post('/api/v1/test/cultural');
      return {
        success: true,
        data: {
          supportedDialects: response.data.supported_dialects,
          features: response.data.features,
          message: response.data.message
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.detail || error.message
      };
    }
  }

  // Utility Methods
  async getSystemStatus() {
    try {
      const [healthCheck, coveHealth] = await Promise.all([
        this.checkHealth(),
        this.checkCOVEIntegrationHealth()
      ]);

      return {
        success: true,
        data: {
          overall: healthCheck.status,
          coveIntegration: coveHealth.isAuthenticated ? 'active' : 'inactive',
          services: {
            integration: healthCheck.success,
            cultural: true, // Assume cultural service is part of integration
            aigency: coveHealth.data?.aigency_backend || 'unknown'
          }
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        data: {
          overall: 'unhealthy',
          coveIntegration: 'inactive',
          services: {
            integration: false,
            cultural: false,
            aigency: false
          }
        }
      };
    }
  }

  // Message Processing (for PAIM integration)
  async processMessage(message, culturalProfile, userPreferences) {
    try {
      // First, detect dialect if the message contains Arabic
      const dialectDetection = await this.detectDialect(message, {
        region: culturalProfile.region
      });

      // Adapt the message for cultural context
      const adaptedMessage = await this.adaptContent(message, culturalProfile);

      // Execute the task through AIgency
      const taskResult = await this.executeTask({
        taskType: 'customer_service',
        content: adaptedMessage.success ? adaptedMessage.data.adaptedContent.content : message,
        culturalContext: culturalProfile,
        userPreferences
      });

      return {
        success: taskResult.success,
        data: {
          response: taskResult.data?.result,
          dialectInfo: dialectDetection.success ? dialectDetection.data : null,
          culturalAdaptation: adaptedMessage.success ? adaptedMessage.data : null,
          agentInfo: taskResult.data ? {
            agentId: taskResult.data.agentId,
            taskType: taskResult.data.taskType
          } : null
        },
        error: taskResult.error
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }
}

// Create singleton instance
const aigencyIntegration = new AIgencyIntegrationService();

export default aigencyIntegration;

// Named exports for specific functionality
export {
  AIgencyIntegrationService,
  aigencyIntegration
};
