# COVE-AIgency Integration Configuration

# Server Configuration
server:
  host: "0.0.0.0"
  port: 8000
  workers: 1
  log_level: "INFO"

# Service URLs
services:
  aigency:
    url: "http://localhost:3000"
    timeout: 30
    max_retries: 3
    retry_delay: 1.0
  
  cove_backend:
    url: "http://localhost:8001"
    timeout: 30
  
  redis:
    url: "redis://localhost:6379"
    timeout: 5

# COVE Authentication
cove:
  email: "<EMAIL>"
  password: "CoveSecure123!"
  organization_id: "cove_org"

# Cultural Adaptation Settings
cultural:
  default_region: "uae"
  supported_dialects:
    - "gulf_uae"
    - "gulf_saudi"
    - "gulf_kuwait"
    - "gulf_qatar"
    - "gulf_bahrain"
    - "gulf_oman"
    - "levantine"
    - "egyptian"
  
  formality_levels:
    - "low"
    - "medium"
    - "high"
  
  religious_sensitivity: true
  gender_considerations: true

# Agent Mapping
agents:
  customer_service: "agent_customer_service"
  content_creation: "agent_content_creator"
  data_analysis: "agent_data_analyst"
  translation: "agent_translator"
  sentiment_analysis: "agent_sentiment_analyzer"
  cultural_consultation: "agent_cultural_consultant"

# PowerOps Configuration
powerops:
  enabled: true
  default_difficulty: "medium"
  default_time_spent: 300
  task_types:
    - "customer_service"
    - "content_creation"
    - "data_analysis"
    - "translation"
    - "cultural_consultation"

# CORS Configuration
cors:
  allowed_origins:
    - "http://localhost:3000"
    - "http://localhost:8080"
    - "http://localhost:3001"
  allow_credentials: true
  allow_methods: ["*"]
  allow_headers: ["*"]

# Logging Configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/cove_integration.log"

# Health Check Configuration
health:
  check_interval: 30
  timeout: 10
  services_to_check:
    - "aigency"
    - "redis"
    - "cultural_service"

# Security Configuration
security:
  jwt_secret: "your-secret-key-change-in-production"
  token_expiry: 3600  # 1 hour
  refresh_token_expiry: 604800  # 7 days

# Feature Flags
features:
  cultural_adaptation: true
  powerops_integration: true
  agent_framework: true
  real_time_monitoring: true
  audit_logging: true

# Performance Configuration
performance:
  max_concurrent_requests: 100
  request_timeout: 30
  connection_pool_size: 20
  keep_alive_timeout: 5

# Monitoring Configuration
monitoring:
  enabled: true
  metrics_endpoint: "/metrics"
  health_endpoint: "/health"
  status_endpoint: "/status"
