/**
 * Enhanced PAIM Provider with AIgency Integration
 * Extends the existing PAIM provider with The AIgency backend integration
 */

import React, { createContext, useContext, useState, useEffect, useCallback, useRef } from 'react';
import { PAIMProvider, usePAIM } from './PAIMProvider';
import aigencyIntegration from '../../services/aigencyIntegration';

// Create Enhanced PAIM Context
const EnhancedPAIMContext = createContext(null);

// Enhanced PAIM Provider Component
export const EnhancedPAIMProvider = ({ children, ...props }) => {
  // State for AIgency integration
  const [aigencyState, setAIgencyState] = useState({
    isConnected: false,
    isAuthenticated: false,
    connectionStatus: 'disconnected',
    lastHealthCheck: null,
    error: null
  });

  const [paimInstances, setPAIMInstances] = useState([]);
  const [powerOpsStats, setPowerOpsStats] = useState(null);
  const [systemStatus, setSystemStatus] = useState(null);

  // Health check interval
  const healthCheckInterval = useRef(null);

  // Initialize AIgency integration
  useEffect(() => {
    const initializeAIgencyIntegration = async () => {
      try {
        console.log('[Enhanced PAIM] Initializing AIgency integration...');
        
        // Check system health
        const healthCheck = await aigencyIntegration.checkHealth();
        const coveHealth = await aigencyIntegration.checkCOVEIntegrationHealth();
        
        setAIgencyState({
          isConnected: healthCheck.success,
          isAuthenticated: coveHealth.isAuthenticated,
          connectionStatus: healthCheck.success ? 'connected' : 'disconnected',
          lastHealthCheck: new Date(),
          error: healthCheck.success ? null : healthCheck.error
        });

        // Load existing PAIMs
        if (healthCheck.success && coveHealth.isAuthenticated) {
          await loadPAIMInstances();
          await loadPowerOpsStats();
        }

        console.log('[Enhanced PAIM] AIgency integration initialized:', {
          connected: healthCheck.success,
          authenticated: coveHealth.isAuthenticated
        });

      } catch (error) {
        console.error('[Enhanced PAIM] Failed to initialize AIgency integration:', error);
        setAIgencyState(prev => ({
          ...prev,
          isConnected: false,
          isAuthenticated: false,
          connectionStatus: 'error',
          error: error.message
        }));
      }
    };

    initializeAIgencyIntegration();

    // Set up periodic health checks
    healthCheckInterval.current = setInterval(async () => {
      try {
        const status = await aigencyIntegration.getSystemStatus();
        setSystemStatus(status.data);
        
        setAIgencyState(prev => ({
          ...prev,
          isConnected: status.success,
          lastHealthCheck: new Date(),
          error: status.success ? null : status.error
        }));
      } catch (error) {
        console.warn('[Enhanced PAIM] Health check failed:', error);
      }
    }, 30000); // Check every 30 seconds

    // Cleanup
    return () => {
      if (healthCheckInterval.current) {
        clearInterval(healthCheckInterval.current);
      }
    };
  }, []);

  // Load PAIM instances
  const loadPAIMInstances = useCallback(async () => {
    try {
      const result = await aigencyIntegration.listPAIMs();
      if (result.success) {
        setPAIMInstances(result.data);
      }
    } catch (error) {
      console.error('[Enhanced PAIM] Failed to load PAIM instances:', error);
    }
  }, []);

  // Load PowerOps stats
  const loadPowerOpsStats = useCallback(async () => {
    try {
      const result = await aigencyIntegration.getPowerOpsStats();
      if (result.success) {
        setPowerOpsStats(result.data);
      }
    } catch (error) {
      console.error('[Enhanced PAIM] Failed to load PowerOps stats:', error);
    }
  }, []);

  // Create PAIM instance
  const createPAIMInstance = useCallback(async (config) => {
    try {
      const result = await aigencyIntegration.createPAIM(config);
      
      if (result.success) {
        // Reload PAIM instances
        await loadPAIMInstances();
        return result;
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('[Enhanced PAIM] Failed to create PAIM instance:', error);
      throw error;
    }
  }, [loadPAIMInstances]);

  // Enhanced message processing
  const processMessageWithAIgency = useCallback(async (message, culturalProfile, userPreferences) => {
    try {
      if (!aigencyState.isConnected || !aigencyState.isAuthenticated) {
        throw new Error('AIgency integration not available');
      }

      const result = await aigencyIntegration.processMessage(message, culturalProfile, userPreferences);
      
      if (result.success) {
        // Update PowerOps stats after successful interaction
        await loadPowerOpsStats();
        
        return {
          success: true,
          data: {
            response: result.data.response,
            metadata: {
              dialectInfo: result.data.dialectInfo,
              culturalAdaptation: result.data.culturalAdaptation,
              agentInfo: result.data.agentInfo,
              responseTime: Date.now(), // Would be provided by backend
              source: 'aigency_integration'
            }
          }
        };
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('[Enhanced PAIM] Message processing failed:', error);
      throw error;
    }
  }, [aigencyState.isConnected, aigencyState.isAuthenticated, loadPowerOpsStats]);

  // Test AIgency connection
  const testAIgencyConnection = useCallback(async () => {
    try {
      const result = await aigencyIntegration.testAIgencyConnection();
      return result;
    } catch (error) {
      console.error('[Enhanced PAIM] AIgency connection test failed:', error);
      return { success: false, error: error.message };
    }
  }, []);

  // Test cultural features
  const testCulturalFeatures = useCallback(async () => {
    try {
      const result = await aigencyIntegration.testCulturalFeatures();
      return result;
    } catch (error) {
      console.error('[Enhanced PAIM] Cultural features test failed:', error);
      return { success: false, error: error.message };
    }
  }, []);

  // Detect dialect
  const detectDialect = useCallback(async (text, context) => {
    try {
      const result = await aigencyIntegration.detectDialect(text, context);
      return result;
    } catch (error) {
      console.error('[Enhanced PAIM] Dialect detection failed:', error);
      return { success: false, error: error.message };
    }
  }, []);

  // Adapt content culturally
  const adaptContent = useCallback(async (content, culturalContext) => {
    try {
      const result = await aigencyIntegration.adaptContent(content, culturalContext);
      return result;
    } catch (error) {
      console.error('[Enhanced PAIM] Content adaptation failed:', error);
      return { success: false, error: error.message };
    }
  }, []);

  // Context value for enhanced features
  const enhancedContextValue = {
    // AIgency integration state
    aigencyState,
    systemStatus,
    
    // PAIM management
    paimInstances,
    createPAIMInstance,
    loadPAIMInstances,
    
    // PowerOps integration
    powerOpsStats,
    loadPowerOpsStats,
    
    // Enhanced messaging
    processMessageWithAIgency,
    
    // Cultural features
    detectDialect,
    adaptContent,
    
    // Testing
    testAIgencyConnection,
    testCulturalFeatures,
    
    // Utilities
    isAIgencyAvailable: aigencyState.isConnected && aigencyState.isAuthenticated,
    lastHealthCheck: aigencyState.lastHealthCheck
  };

  return (
    <PAIMProvider {...props}>
      <EnhancedPAIMContext.Provider value={enhancedContextValue}>
        {children}
      </EnhancedPAIMContext.Provider>
    </PAIMProvider>
  );
};

// Hook to use Enhanced PAIM context
export const useEnhancedPAIM = () => {
  const context = useContext(EnhancedPAIMContext);
  if (!context) {
    throw new Error('useEnhancedPAIM must be used within an EnhancedPAIMProvider');
  }
  return context;
};

// Combined hook that provides both original and enhanced PAIM functionality
export const useFullPAIM = () => {
  const originalPAIM = usePAIM();
  const enhancedPAIM = useEnhancedPAIM();
  
  return {
    ...originalPAIM,
    ...enhancedPAIM,
    
    // Enhanced send message that tries AIgency first, falls back to original
    sendMessage: async (content) => {
      try {
        if (enhancedPAIM.isAIgencyAvailable) {
          return await enhancedPAIM.processMessageWithAIgency(
            content,
            originalPAIM.culturalProfile,
            originalPAIM.userPreferences
          );
        } else {
          // Fallback to original PAIM
          return await originalPAIM.sendMessage(content);
        }
      } catch (error) {
        console.warn('[Full PAIM] AIgency processing failed, falling back to original PAIM:', error);
        return await originalPAIM.sendMessage(content);
      }
    }
  };
};

// Specialized hooks for enhanced features

/**
 * Hook for AIgency integration management
 */
export const useAIgencyIntegration = () => {
  const { 
    aigencyState, 
    systemStatus, 
    testAIgencyConnection, 
    testCulturalFeatures,
    isAIgencyAvailable,
    lastHealthCheck
  } = useEnhancedPAIM();

  return {
    aigencyState,
    systemStatus,
    testAIgencyConnection,
    testCulturalFeatures,
    isAIgencyAvailable,
    lastHealthCheck
  };
};

/**
 * Hook for PAIM instance management
 */
export const usePAIMManagement = () => {
  const { 
    paimInstances, 
    createPAIMInstance, 
    loadPAIMInstances 
  } = useEnhancedPAIM();

  return {
    paimInstances,
    createPAIMInstance,
    loadPAIMInstances,
    totalPAIMs: paimInstances.length,
    activePAIMs: paimInstances.filter(paim => paim.status === 'active').length
  };
};

/**
 * Hook for PowerOps integration
 */
export const usePowerOps = () => {
  const { 
    powerOpsStats, 
    loadPowerOpsStats 
  } = useEnhancedPAIM();

  return {
    powerOpsStats,
    loadPowerOpsStats,
    userLevel: powerOpsStats?.stats?.level || 1,
    experience: powerOpsStats?.stats?.experience || 0,
    activePAIMs: powerOpsStats?.coveEnhancements?.active_paims || 0
  };
};

/**
 * Hook for enhanced cultural features
 */
export const useEnhancedCultural = () => {
  const { detectDialect, adaptContent } = useEnhancedPAIM();
  const { culturalProfile, updateCulturalProfile } = usePAIM();

  const detectAndAdaptMessage = useCallback(async (message) => {
    try {
      // Detect dialect
      const dialectResult = await detectDialect(message, {
        region: culturalProfile.region
      });

      // Adapt content
      const adaptationResult = await adaptContent(message, culturalProfile);

      return {
        success: true,
        data: {
          originalMessage: message,
          dialectInfo: dialectResult.success ? dialectResult.data : null,
          adaptedContent: adaptationResult.success ? adaptationResult.data : null
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }, [detectDialect, adaptContent, culturalProfile]);

  return {
    detectDialect,
    adaptContent,
    detectAndAdaptMessage,
    culturalProfile,
    updateCulturalProfile
  };
};
