"""
COVE Integration Service - Main service for COVE-AIgency integration
Coordinates COVE's cultural features with The AIgency backend
"""

import asyncio
import json
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timezone
from dataclasses import dataclass

from fastapi import FastAPI, HTTPException, BackgroundTasks
from pydantic import BaseModel, Field

from aigency_client import create_aigency_client, AIgencyClient
from cultural.adaptation_service import create_cultural_adaptation_service

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class COVEPAIMRequest(BaseModel):
    """Request to create COVE PAIM"""
    name: str = Field(..., description="PAIM name")
    description: str = Field(..., description="PAIM description")
    cultural_region: str = Field(default="uae", description="Cultural region")
    dialect_support: List[str] = Field(default=["gulf_uae"], description="Supported dialects")
    capabilities: List[str] = Field(default_factory=list, description="PAIM capabilities")

class COVETaskRequest(BaseModel):
    """Request to execute COVE task"""
    task_type: str = Field(..., description="Type of task")
    content: str = Field(..., description="Task content")
    cultural_context: Dict[str, Any] = Field(default_factory=dict, description="Cultural context")
    user_preferences: Dict[str, Any] = Field(default_factory=dict, description="User preferences")

class COVEIntegrationService:
    """Main COVE Integration Service"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.app = FastAPI(title="COVE Integration Service", version="1.0.0")
        
        # Initialize AIgency client
        self.aigency_client = create_aigency_client(
            base_url=config.get("aigency_url", "http://localhost:3000")
        )
        
        # Initialize cultural service
        self.cultural_service = create_cultural_adaptation_service(config.get("cultural", {}))
        
        # COVE user credentials
        self.cove_email = config.get("cove_email", "<EMAIL>")
        self.cove_password = config.get("cove_password", "CoveSecure123!")
        
        # Authentication state
        self.is_authenticated = False
        self.paim_instances: Dict[str, Dict[str, Any]] = {}
        
        # Setup routes
        self._setup_routes()
    
    def _setup_routes(self):
        """Setup API routes"""
        
        @self.app.on_event("startup")
        async def startup_event():
            """Initialize COVE integration on startup"""
            await self.initialize_cove_integration()
        
        @self.app.on_event("shutdown")
        async def shutdown_event():
            """Cleanup on shutdown"""
            await self.aigency_client.close()
        
        @self.app.get("/health")
        async def health_check():
            """Health check endpoint"""
            aigency_health = await self.aigency_client.health_check()
            
            return {
                "status": "healthy" if aigency_health["success"] else "degraded",
                "cove_integration": "active" if self.is_authenticated else "inactive",
                "aigency_backend": aigency_health["success"],
                "paim_instances": len(self.paim_instances),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        
        @self.app.post("/api/v1/cove/paim/create")
        async def create_cove_paim(request: COVEPAIMRequest):
            """Create a COVE PAIM with cultural capabilities"""
            try:
                if not self.is_authenticated:
                    await self.initialize_cove_integration()
                
                # Enhance capabilities with COVE features
                enhanced_capabilities = request.capabilities + [
                    "cultural_adaptation",
                    "arabic_dialect_support",
                    "regional_compliance",
                    "sentiment_analysis"
                ]
                
                # Create PAIM configuration with cultural settings
                configuration = {
                    "model": "gpt-4",
                    "temperature": 0.7,
                    "maxTokens": 2048,
                    "coveIntegration": True,
                    "culturalAdaptation": True,
                    "arabicDialectSupport": True,
                    "supportedDialects": request.dialect_support,
                    "culturalRegion": request.cultural_region,
                    "religiousSensitivity": True,
                    "formalityAdaptation": True
                }
                
                # Create PAIM in The AIgency
                paim_response = await self.aigency_client.create_paim(
                    name=request.name,
                    description=request.description,
                    paim_type="cove_cultural_paim",
                    capabilities=enhanced_capabilities,
                    configuration=configuration
                )
                
                if paim_response.success:
                    paim_data = paim_response.data.get("data", {})
                    paim_id = paim_data.get("id")
                    
                    # Store PAIM instance locally
                    self.paim_instances[paim_id] = {
                        "id": paim_id,
                        "name": request.name,
                        "cultural_region": request.cultural_region,
                        "dialect_support": request.dialect_support,
                        "created_at": datetime.now(timezone.utc).isoformat(),
                        "status": "created"
                    }
                    
                    # Activate the PAIM
                    activation_response = await self.aigency_client.activate_paim(paim_id)
                    
                    if activation_response.success:
                        self.paim_instances[paim_id]["status"] = "active"
                        
                        return {
                            "success": True,
                            "paim_id": paim_id,
                            "paim_data": paim_data,
                            "cove_features": {
                                "cultural_region": request.cultural_region,
                                "dialect_support": request.dialect_support,
                                "cultural_adaptation": True
                            },
                            "status": "active"
                        }
                    else:
                        return {
                            "success": False,
                            "error": "PAIM created but activation failed",
                            "paim_id": paim_id,
                            "activation_error": activation_response.error
                        }
                else:
                    return {
                        "success": False,
                        "error": "Failed to create PAIM",
                        "details": paim_response.error
                    }
                    
            except Exception as e:
                logger.error(f"Error creating COVE PAIM: {str(e)}")
                raise HTTPException(status_code=500, detail=f"PAIM creation failed: {str(e)}")
        
        @self.app.post("/api/v1/cove/task/execute")
        async def execute_cove_task(
            request: COVETaskRequest,
            background_tasks: BackgroundTasks
        ):
            """Execute a COVE task with cultural adaptation"""
            try:
                if not self.is_authenticated:
                    await self.initialize_cove_integration()
                
                # Apply cultural adaptation to the content
                adapted_content = await self._apply_cultural_adaptation(
                    request.content,
                    request.cultural_context
                )
                
                # Determine the appropriate agent based on task type
                agent_id = self._get_agent_for_task(request.task_type)
                
                if not agent_id:
                    return {
                        "success": False,
                        "error": f"No agent available for task type: {request.task_type}"
                    }
                
                # Execute the agent with adapted content
                agent_response = await self.aigency_client.execute_agent(
                    agent_id=agent_id,
                    query=adapted_content,
                    parameters={
                        "task_type": request.task_type,
                        "cultural_region": request.cultural_context.get("region", "uae"),
                        "dialect": request.cultural_context.get("dialect", "gulf_uae"),
                        "formality_level": request.cultural_context.get("formality_level", "medium")
                    },
                    context={
                        "source": "cove_paim",
                        "cultural_adaptation": True,
                        "user_preferences": request.user_preferences
                    }
                )
                
                if agent_response.success:
                    # Apply final cultural adaptation to the response
                    final_response = await self._adapt_agent_response(
                        agent_response.data,
                        request.cultural_context
                    )
                    
                    # Log task completion for PowerOps
                    background_tasks.add_task(
                        self._log_task_completion,
                        request.task_type,
                        "medium",
                        300
                    )
                    
                    return {
                        "success": True,
                        "result": final_response,
                        "cultural_adaptations_applied": True,
                        "agent_id": agent_id,
                        "task_type": request.task_type
                    }
                else:
                    return {
                        "success": False,
                        "error": "Agent execution failed",
                        "details": agent_response.error
                    }
                    
            except Exception as e:
                logger.error(f"Error executing COVE task: {str(e)}")
                raise HTTPException(status_code=500, detail=f"Task execution failed: {str(e)}")
        
        @self.app.get("/api/v1/cove/paims")
        async def list_cove_paims():
            """List all COVE PAIM instances"""
            try:
                if not self.is_authenticated:
                    await self.initialize_cove_integration()
                
                # Get PAIMs from The AIgency
                paims_response = await self.aigency_client.list_paims()
                
                if paims_response.success:
                    aigency_paims = paims_response.data.get("data", [])
                    
                    # Filter for COVE PAIMs and enhance with local data
                    cove_paims = []
                    for paim in aigency_paims:
                        paim_id = paim.get("id")
                        if paim_id in self.paim_instances:
                            enhanced_paim = {
                                **paim,
                                **self.paim_instances[paim_id]
                            }
                            cove_paims.append(enhanced_paim)
                    
                    return {
                        "success": True,
                        "paims": cove_paims,
                        "total": len(cove_paims)
                    }
                else:
                    return {
                        "success": False,
                        "error": "Failed to list PAIMs",
                        "details": paims_response.error
                    }
                    
            except Exception as e:
                logger.error(f"Error listing COVE PAIMs: {str(e)}")
                raise HTTPException(status_code=500, detail=f"Failed to list PAIMs: {str(e)}")
        
        @self.app.get("/api/v1/cove/powerops/stats")
        async def get_cove_powerops_stats():
            """Get PowerOps statistics for COVE user"""
            try:
                if not self.is_authenticated:
                    await self.initialize_cove_integration()
                
                stats_response = await self.aigency_client.get_user_stats()
                
                if stats_response["success"]:
                    return {
                        "success": True,
                        "stats": stats_response["data"],
                        "cove_enhancements": {
                            "cultural_tasks_completed": len(self.paim_instances),
                            "active_paims": len([p for p in self.paim_instances.values() if p["status"] == "active"])
                        }
                    }
                else:
                    return {
                        "success": False,
                        "error": "Failed to get PowerOps stats",
                        "details": stats_response["error"]
                    }
                    
            except Exception as e:
                logger.error(f"Error getting PowerOps stats: {str(e)}")
                raise HTTPException(status_code=500, detail=f"Failed to get stats: {str(e)}")
    
    async def initialize_cove_integration(self):
        """Initialize COVE integration with The AIgency"""
        try:
            logger.info("Initializing COVE integration with The AIgency...")
            
            # Check if The AIgency is healthy
            health_check = await self.aigency_client.health_check()
            if not health_check["success"]:
                raise Exception("The AIgency backend is not healthy")
            
            # Try to login first
            login_response = await self.aigency_client.login(
                email=self.cove_email,
                password=self.cove_password
            )
            
            if login_response.success:
                self.is_authenticated = True
                logger.info("Successfully logged in to The AIgency")
            else:
                # If login fails, try to register
                logger.info("Login failed, attempting to register COVE user...")
                register_response = await self.aigency_client.register_cove_user(
                    email=self.cove_email,
                    password=self.cove_password,
                    first_name="COVE",
                    last_name="PAIM",
                    organization_id="cove_org"
                )
                
                if register_response.success:
                    self.is_authenticated = True
                    logger.info("Successfully registered and authenticated COVE user")
                else:
                    raise Exception(f"Failed to authenticate COVE user: {register_response.error}")
            
            logger.info("COVE integration initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize COVE integration: {str(e)}")
            raise

    async def _apply_cultural_adaptation(self, content: str, cultural_context: Dict[str, Any]) -> str:
        """Apply cultural adaptation to content"""
        try:
            # Use the cultural adaptation service
            import httpx
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.config.get('base_url', 'http://localhost:8000')}/cultural/api/v1/cultural/adapt",
                    json={
                        "response": {"content": content},
                        "cultural_context": cultural_context
                    },
                    timeout=10.0
                )

                if response.status_code == 200:
                    adaptation_result = response.json()
                    adapted_content = adaptation_result.get("adapted_content", {})
                    return adapted_content.get("content", content)
                else:
                    logger.warning(f"Cultural adaptation failed: {response.status_code}")
                    return content

        except Exception as e:
            logger.warning(f"Cultural adaptation error: {str(e)}")
            return content

    async def _adapt_agent_response(self, response_data: Dict[str, Any], cultural_context: Dict[str, Any]) -> Dict[str, Any]:
        """Apply cultural adaptation to agent response"""
        try:
            # Use the cultural adaptation service
            import httpx
            async with httpx.AsyncClient() as client:
                adaptation_response = await client.post(
                    f"{self.config.get('base_url', 'http://localhost:8000')}/cultural/api/v1/cultural/adapt",
                    json={
                        "response": response_data,
                        "cultural_context": cultural_context
                    },
                    timeout=10.0
                )

                if adaptation_response.status_code == 200:
                    adaptation_result = adaptation_response.json()
                    return adaptation_result.get("adapted_content", response_data)
                else:
                    logger.warning(f"Response adaptation failed: {adaptation_response.status_code}")
                    return response_data

        except Exception as e:
            logger.warning(f"Response adaptation error: {str(e)}")
            return response_data

    def _get_agent_for_task(self, task_type: str) -> Optional[str]:
        """Get appropriate agent ID for task type"""
        # Map task types to agent IDs (these would be actual agent IDs from The AIgency)
        agent_mapping = {
            "customer_service": "agent_customer_service",
            "content_creation": "agent_content_creator",
            "data_analysis": "agent_data_analyst",
            "translation": "agent_translator",
            "sentiment_analysis": "agent_sentiment_analyzer",
            "cultural_consultation": "agent_cultural_consultant"
        }

        return agent_mapping.get(task_type)

    async def _log_task_completion(self, task_type: str, difficulty: str, time_spent: int):
        """Log task completion for PowerOps"""
        try:
            task_id = f"cove_{task_type}_{datetime.now(timezone.utc).timestamp()}"

            completion_response = await self.aigency_client.complete_task(
                task_id=task_id,
                task_type=f"cove_{task_type}",
                difficulty=difficulty,
                time_spent=time_spent
            )

            if completion_response["success"]:
                logger.info(f"Successfully logged task completion: {task_id}")
            else:
                logger.warning(f"Failed to log task completion: {completion_response['error']}")

        except Exception as e:
            logger.warning(f"Error logging task completion: {str(e)}")

    def get_app(self) -> FastAPI:
        """Get the FastAPI application instance"""
        return self.app


# Factory function to create COVE integration service
def create_cove_integration_service(config: Dict[str, Any]) -> COVEIntegrationService:
    """Create and configure COVE Integration Service instance"""
    return COVEIntegrationService(config)
