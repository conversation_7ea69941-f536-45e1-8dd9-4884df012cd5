@echo off
echo ========================================
echo Starting COVE Frontend with AIgency Integration
echo ========================================

echo.
echo Checking if COVE Integration Service is running...
curl -s http://localhost:8000/health >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ COVE Integration Service is not running on localhost:8000
    echo Please start the integration service first:
    echo    cd backend/services
    echo    python simple_start.py
    echo.
    pause
    exit /b 1
) else (
    echo ✅ COVE Integration Service is running
)

echo.
echo Checking Node.js and npm...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
) else (
    echo ✅ Node.js is available
)

npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm is not available
    pause
    exit /b 1
) else (
    echo ✅ npm is available
)

echo.
echo Installing dependencies...
npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo ========================================
echo Starting COVE Frontend...
echo ========================================
echo.
echo The frontend will be available at:
echo   http://localhost:3000
echo.
echo Features available:
echo   ✅ Design System Demo
echo   ✅ Smart Canvas
echo   ✅ PAIM Assistant
echo   ✅ Voice Demo
echo   🆕 AIgency Integration Demo
echo.
echo The AIgency Integration tab will show:
echo   - System status and health checks
echo   - PAIM instance management
echo   - Cultural adaptation testing
echo   - PowerOps statistics
echo   - Integration testing tools
echo.
echo Press Ctrl+C to stop the server
echo ========================================

npm start
