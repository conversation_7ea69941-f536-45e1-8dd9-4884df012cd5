"""
COVE-AIgency Integration Test Script
Tests the integration between COVE and The AIgency backend
"""

import asyncio
import json
import logging
from typing import Dict, Any

import httpx

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class COVEIntegrationTester:
    """Test suite for COVE-AIgency integration"""
    
    def __init__(self):
        self.cove_base_url = "http://localhost:8000"
        self.aigency_base_url = "http://localhost:3000"
        self.test_results = []
    
    async def run_all_tests(self):
        """Run all integration tests"""
        logger.info("Starting COVE-AIgency Integration Tests...")
        
        tests = [
            ("Health Checks", self.test_health_checks),
            ("COVE Service Initialization", self.test_cove_service_init),
            ("Cultural Adaptation", self.test_cultural_adaptation),
            ("PAIM Creation", self.test_paim_creation),
            ("Task Execution", self.test_task_execution),
            ("PowerOps Integration", self.test_powerops_integration),
            ("Agent Framework", self.test_agent_framework)
        ]
        
        for test_name, test_func in tests:
            logger.info(f"\n{'='*50}")
            logger.info(f"Running Test: {test_name}")
            logger.info(f"{'='*50}")
            
            try:
                result = await test_func()
                self.test_results.append({
                    "test": test_name,
                    "status": "PASSED" if result else "FAILED",
                    "details": result
                })
                logger.info(f"✅ {test_name}: {'PASSED' if result else 'FAILED'}")
            except Exception as e:
                self.test_results.append({
                    "test": test_name,
                    "status": "ERROR",
                    "error": str(e)
                })
                logger.error(f"❌ {test_name}: ERROR - {str(e)}")
        
        # Print summary
        self.print_test_summary()
    
    async def test_health_checks(self) -> bool:
        """Test health checks for all services"""
        try:
            async with httpx.AsyncClient() as client:
                # Test The AIgency backend
                logger.info("Testing The AIgency backend health...")
                aigency_health = await client.get(f"{self.aigency_base_url}/health", timeout=10.0)
                logger.info(f"AIgency Health: {aigency_health.status_code}")
                
                # Test COVE integration service
                logger.info("Testing COVE integration service health...")
                cove_health = await client.get(f"{self.cove_base_url}/health", timeout=10.0)
                logger.info(f"COVE Health: {cove_health.status_code}")
                
                # Test cultural adaptation service
                logger.info("Testing Cultural adaptation service health...")
                cultural_health = await client.get(f"{self.cove_base_url}/cultural/health", timeout=10.0)
                logger.info(f"Cultural Health: {cultural_health.status_code}")
                
                return (aigency_health.status_code == 200 and 
                       cove_health.status_code == 200 and 
                       cultural_health.status_code == 200)
                
        except Exception as e:
            logger.error(f"Health check failed: {str(e)}")
            return False
    
    async def test_cove_service_init(self) -> bool:
        """Test COVE service initialization"""
        try:
            async with httpx.AsyncClient() as client:
                # Test COVE service health
                response = await client.get(f"{self.cove_base_url}/cove/health", timeout=10.0)
                
                if response.status_code == 200:
                    data = response.json()
                    logger.info(f"COVE Service Status: {data}")
                    
                    # Check if COVE is authenticated with The AIgency
                    is_authenticated = data.get("cove_integration") == "active"
                    logger.info(f"COVE Authentication Status: {'✅ Active' if is_authenticated else '❌ Inactive'}")
                    
                    return is_authenticated
                else:
                    logger.error(f"COVE service health check failed: {response.status_code}")
                    return False
                    
        except Exception as e:
            logger.error(f"COVE service initialization test failed: {str(e)}")
            return False
    
    async def test_cultural_adaptation(self) -> bool:
        """Test cultural adaptation functionality"""
        try:
            async with httpx.AsyncClient() as client:
                # Test dialect detection
                logger.info("Testing dialect detection...")
                dialect_response = await client.post(
                    f"{self.cove_base_url}/cultural/api/v1/cultural/detect-dialect",
                    json={
                        "text": "شلونك؟ وين رايح اليوم؟",
                        "context": {"region": "uae"}
                    },
                    timeout=10.0
                )
                
                if dialect_response.status_code == 200:
                    dialect_data = dialect_response.json()
                    logger.info(f"Detected Dialect: {dialect_data.get('detected_dialect')}")
                    logger.info(f"Confidence: {dialect_data.get('confidence')}")
                else:
                    logger.error(f"Dialect detection failed: {dialect_response.status_code}")
                    return False
                
                # Test cultural adaptation
                logger.info("Testing cultural adaptation...")
                adaptation_response = await client.post(
                    f"{self.cove_base_url}/cultural/api/v1/cultural/adapt",
                    json={
                        "response": {"content": "Hello, how are you today?"},
                        "cultural_context": {
                            "region": "uae",
                            "formality_level": "high",
                            "religious_considerations": True
                        }
                    },
                    timeout=10.0
                )
                
                if adaptation_response.status_code == 200:
                    adaptation_data = adaptation_response.json()
                    logger.info(f"Cultural Adaptation Applied: {adaptation_data.get('cultural_appropriateness_score')}")
                    return True
                else:
                    logger.error(f"Cultural adaptation failed: {adaptation_response.status_code}")
                    return False
                    
        except Exception as e:
            logger.error(f"Cultural adaptation test failed: {str(e)}")
            return False
    
    async def test_paim_creation(self) -> bool:
        """Test PAIM creation through COVE integration"""
        try:
            async with httpx.AsyncClient() as client:
                logger.info("Testing COVE PAIM creation...")
                
                paim_data = {
                    "name": "Test COVE PAIM",
                    "description": "Test PAIM for integration testing",
                    "cultural_region": "uae",
                    "dialect_support": ["gulf_uae", "gulf_saudi"],
                    "capabilities": ["chat", "email", "knowledge_base"]
                }
                
                response = await client.post(
                    f"{self.cove_base_url}/cove/api/v1/cove/paim/create",
                    json=paim_data,
                    timeout=30.0
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get("success"):
                        paim_id = data.get("paim_id")
                        logger.info(f"✅ PAIM Created Successfully: {paim_id}")
                        logger.info(f"PAIM Status: {data.get('status')}")
                        logger.info(f"COVE Features: {data.get('cove_features')}")
                        return True
                    else:
                        logger.error(f"PAIM creation failed: {data.get('error')}")
                        return False
                else:
                    logger.error(f"PAIM creation request failed: {response.status_code}")
                    return False
                    
        except Exception as e:
            logger.error(f"PAIM creation test failed: {str(e)}")
            return False
    
    async def test_task_execution(self) -> bool:
        """Test task execution with cultural adaptation"""
        try:
            async with httpx.AsyncClient() as client:
                logger.info("Testing COVE task execution...")
                
                task_data = {
                    "task_type": "customer_service",
                    "content": "I need help with my account",
                    "cultural_context": {
                        "region": "uae",
                        "dialect": "gulf_uae",
                        "formality_level": "medium",
                        "religious_considerations": True
                    },
                    "user_preferences": {
                        "language": "arabic",
                        "response_style": "helpful"
                    }
                }
                
                response = await client.post(
                    f"{self.cove_base_url}/cove/api/v1/cove/task/execute",
                    json=task_data,
                    timeout=30.0
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get("success"):
                        logger.info(f"✅ Task Executed Successfully")
                        logger.info(f"Agent ID: {data.get('agent_id')}")
                        logger.info(f"Cultural Adaptations Applied: {data.get('cultural_adaptations_applied')}")
                        return True
                    else:
                        logger.error(f"Task execution failed: {data.get('error')}")
                        return False
                else:
                    logger.error(f"Task execution request failed: {response.status_code}")
                    return False
                    
        except Exception as e:
            logger.error(f"Task execution test failed: {str(e)}")
            return False
    
    async def test_powerops_integration(self) -> bool:
        """Test PowerOps integration"""
        try:
            async with httpx.AsyncClient() as client:
                logger.info("Testing PowerOps integration...")
                
                response = await client.get(
                    f"{self.cove_base_url}/cove/api/v1/cove/powerops/stats",
                    timeout=10.0
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get("success"):
                        stats = data.get("stats", {})
                        cove_enhancements = data.get("cove_enhancements", {})
                        
                        logger.info(f"✅ PowerOps Stats Retrieved")
                        logger.info(f"User Level: {stats.get('level')}")
                        logger.info(f"Experience: {stats.get('experience')}")
                        logger.info(f"COVE PAIMs: {cove_enhancements.get('active_paims')}")
                        return True
                    else:
                        logger.error(f"PowerOps stats failed: {data.get('error')}")
                        return False
                else:
                    logger.error(f"PowerOps request failed: {response.status_code}")
                    return False
                    
        except Exception as e:
            logger.error(f"PowerOps integration test failed: {str(e)}")
            return False
    
    async def test_agent_framework(self) -> bool:
        """Test agent framework integration"""
        try:
            async with httpx.AsyncClient() as client:
                logger.info("Testing Agent Framework integration...")
                
                # Test listing agents through The AIgency
                response = await client.get(
                    f"{self.aigency_base_url}/api/v1/agents",
                    timeout=10.0
                )
                
                if response.status_code == 200:
                    logger.info("✅ Agent Framework accessible")
                    return True
                else:
                    logger.warning(f"Agent Framework test inconclusive: {response.status_code}")
                    return True  # Not critical for basic integration
                    
        except Exception as e:
            logger.warning(f"Agent framework test failed: {str(e)}")
            return True  # Not critical for basic integration
    
    def print_test_summary(self):
        """Print test summary"""
        logger.info(f"\n{'='*60}")
        logger.info("COVE-AIgency Integration Test Summary")
        logger.info(f"{'='*60}")
        
        passed = sum(1 for result in self.test_results if result["status"] == "PASSED")
        failed = sum(1 for result in self.test_results if result["status"] == "FAILED")
        errors = sum(1 for result in self.test_results if result["status"] == "ERROR")
        
        logger.info(f"Total Tests: {len(self.test_results)}")
        logger.info(f"✅ Passed: {passed}")
        logger.info(f"❌ Failed: {failed}")
        logger.info(f"🔥 Errors: {errors}")
        
        if failed == 0 and errors == 0:
            logger.info("\n🎉 ALL TESTS PASSED! COVE-AIgency integration is working correctly.")
        else:
            logger.info("\n⚠️  Some tests failed. Please check the logs above for details.")
        
        logger.info(f"\n{'='*60}")

async def main():
    """Main test runner"""
    tester = COVEIntegrationTester()
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
