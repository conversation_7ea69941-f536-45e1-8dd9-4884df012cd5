/**
 * AIgency Integration Demo Component
 * Demonstrates the integration between COVE and The AIgency backend
 */

import React, { useState, useEffect } from 'react';
import { 
  useAIgencyIntegration, 
  usePAIMManagement, 
  usePowerOps, 
  useEnhancedCultural,
  useFullPAIM 
} from '../paim/providers/EnhancedPAIMProvider';
import { useTheme, Button, Text, Container, Input } from '../design-system';

const AIgencyIntegrationDemo = () => {
  const { language } = useTheme();
  const [activeTab, setActiveTab] = useState('status');
  const [testMessage, setTestMessage] = useState('');
  const [testResults, setTestResults] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  // Enhanced PAIM hooks
  const { 
    aigencyState, 
    systemStatus, 
    testAIgencyConnection, 
    testCulturalFeatures,
    isAIgencyAvailable 
  } = useAIgencyIntegration();

  const { 
    paimInstances, 
    createPAIMInstance, 
    totalPAIMs, 
    activePAIMs 
  } = usePAIMManagement();

  const { 
    powerOpsStats, 
    userLevel, 
    experience 
  } = usePowerOps();

  const { 
    detectAndAdaptMessage 
  } = useEnhancedCultural();

  const { sendMessage } = useFullPAIM();

  // Test functions
  const runConnectionTest = async () => {
    setIsLoading(true);
    try {
      const result = await testAIgencyConnection();
      setTestResults(prev => ({
        ...prev,
        connectionTest: result
      }));
    } catch (error) {
      setTestResults(prev => ({
        ...prev,
        connectionTest: { success: false, error: error.message }
      }));
    }
    setIsLoading(false);
  };

  const runCulturalTest = async () => {
    setIsLoading(true);
    try {
      const result = await testCulturalFeatures();
      setTestResults(prev => ({
        ...prev,
        culturalTest: result
      }));
    } catch (error) {
      setTestResults(prev => ({
        ...prev,
        culturalTest: { success: false, error: error.message }
      }));
    }
    setIsLoading(false);
  };

  const testMessageProcessing = async () => {
    if (!testMessage.trim()) return;
    
    setIsLoading(true);
    try {
      // Test cultural adaptation
      const culturalResult = await detectAndAdaptMessage(testMessage);
      
      // Test full message processing
      const messageResult = await sendMessage(testMessage);
      
      setTestResults(prev => ({
        ...prev,
        messageTest: {
          cultural: culturalResult,
          processing: messageResult
        }
      }));
    } catch (error) {
      setTestResults(prev => ({
        ...prev,
        messageTest: { success: false, error: error.message }
      }));
    }
    setIsLoading(false);
  };

  const createTestPAIM = async () => {
    setIsLoading(true);
    try {
      const result = await createPAIMInstance({
        name: 'Test COVE PAIM',
        description: 'Test PAIM created from COVE UI',
        culturalRegion: 'uae',
        dialectSupport: ['gulf_uae', 'gulf_saudi'],
        capabilities: ['chat', 'cultural_adaptation', 'sentiment_analysis']
      });
      
      setTestResults(prev => ({
        ...prev,
        paimCreation: result
      }));
    } catch (error) {
      setTestResults(prev => ({
        ...prev,
        paimCreation: { success: false, error: error.message }
      }));
    }
    setIsLoading(false);
  };

  // Status indicators
  const getStatusColor = (status) => {
    switch (status) {
      case 'connected':
      case 'active':
      case 'healthy':
        return 'var(--cove-color-success-default)';
      case 'connecting':
      case 'inactive':
        return 'var(--cove-color-warning-default)';
      case 'disconnected':
      case 'error':
      case 'unhealthy':
        return 'var(--cove-color-error-default)';
      default:
        return 'var(--cove-color-neutral-default)';
    }
  };

  const StatusIndicator = ({ status, label }) => (
    <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '0.5rem' }}>
      <div 
        style={{
          width: '12px',
          height: '12px',
          borderRadius: '50%',
          backgroundColor: getStatusColor(status)
        }}
      />
      <Text variant="bodySmall">
        {label}: <strong>{status}</strong>
      </Text>
    </div>
  );

  const tabs = [
    { id: 'status', label: language === 'ar' ? 'الحالة' : 'Status' },
    { id: 'paims', label: language === 'ar' ? 'إدارة PAIM' : 'PAIM Management' },
    { id: 'cultural', label: language === 'ar' ? 'التكيف الثقافي' : 'Cultural Adaptation' },
    { id: 'powerops', label: language === 'ar' ? 'PowerOps' : 'PowerOps' },
    { id: 'testing', label: language === 'ar' ? 'الاختبار' : 'Testing' }
  ];

  return (
    <Container padding="lg">
      <Text variant="h2" style={{ marginBottom: '2rem' }}>
        {language === 'ar' ? 'تكامل AIgency' : 'AIgency Integration'}
      </Text>

      {/* Tab Navigation */}
      <div style={{ 
        display: 'flex', 
        gap: '1rem', 
        marginBottom: '2rem',
        borderBottom: '1px solid var(--cove-color-border-light)',
        paddingBottom: '1rem'
      }}>
        {tabs.map(tab => (
          <Button
            key={tab.id}
            variant={activeTab === tab.id ? 'primary' : 'secondary'}
            size="sm"
            onClick={() => setActiveTab(tab.id)}
          >
            {tab.label}
          </Button>
        ))}
      </div>

      {/* Status Tab */}
      {activeTab === 'status' && (
        <div>
          <Text variant="h3" style={{ marginBottom: '1rem' }}>
            {language === 'ar' ? 'حالة النظام' : 'System Status'}
          </Text>
          
          <div style={{ 
            display: 'grid', 
            gap: '1rem',
            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))'
          }}>
            <div style={{ 
              padding: '1rem', 
              border: '1px solid var(--cove-color-border-light)',
              borderRadius: '8px'
            }}>
              <Text variant="h4" style={{ marginBottom: '1rem' }}>
                {language === 'ar' ? 'تكامل AIgency' : 'AIgency Integration'}
              </Text>
              <StatusIndicator 
                status={aigencyState.connectionStatus} 
                label={language === 'ar' ? 'الاتصال' : 'Connection'} 
              />
              <StatusIndicator 
                status={aigencyState.isAuthenticated ? 'active' : 'inactive'} 
                label={language === 'ar' ? 'المصادقة' : 'Authentication'} 
              />
              {aigencyState.lastHealthCheck && (
                <Text variant="bodySmall" style={{ marginTop: '0.5rem' }}>
                  {language === 'ar' ? 'آخر فحص: ' : 'Last check: '}
                  {aigencyState.lastHealthCheck.toLocaleTimeString()}
                </Text>
              )}
            </div>

            {systemStatus && (
              <div style={{ 
                padding: '1rem', 
                border: '1px solid var(--cove-color-border-light)',
                borderRadius: '8px'
              }}>
                <Text variant="h4" style={{ marginBottom: '1rem' }}>
                  {language === 'ar' ? 'الخدمات' : 'Services'}
                </Text>
                <StatusIndicator 
                  status={systemStatus.overall} 
                  label={language === 'ar' ? 'النظام العام' : 'Overall System'} 
                />
                <StatusIndicator 
                  status={systemStatus.coveIntegration} 
                  label={language === 'ar' ? 'تكامل COVE' : 'COVE Integration'} 
                />
                <StatusIndicator 
                  status={systemStatus.services?.aigency ? 'connected' : 'disconnected'} 
                  label={language === 'ar' ? 'خلفية AIgency' : 'AIgency Backend'} 
                />
              </div>
            )}
          </div>
        </div>
      )}

      {/* PAIM Management Tab */}
      {activeTab === 'paims' && (
        <div>
          <Text variant="h3" style={{ marginBottom: '1rem' }}>
            {language === 'ar' ? 'إدارة PAIM' : 'PAIM Management'}
          </Text>
          
          <div style={{ marginBottom: '2rem' }}>
            <Text variant="body" style={{ marginBottom: '1rem' }}>
              {language === 'ar' 
                ? `إجمالي PAIMs: ${totalPAIMs} | نشط: ${activePAIMs}`
                : `Total PAIMs: ${totalPAIMs} | Active: ${activePAIMs}`
              }
            </Text>
            
            <Button 
              onClick={createTestPAIM} 
              disabled={isLoading || !isAIgencyAvailable}
              style={{ marginBottom: '1rem' }}
            >
              {language === 'ar' ? 'إنشاء PAIM تجريبي' : 'Create Test PAIM'}
            </Button>
          </div>

          {paimInstances.length > 0 && (
            <div>
              <Text variant="h4" style={{ marginBottom: '1rem' }}>
                {language === 'ar' ? 'مثيلات PAIM' : 'PAIM Instances'}
              </Text>
              {paimInstances.map((paim, index) => (
                <div 
                  key={index}
                  style={{ 
                    padding: '1rem', 
                    border: '1px solid var(--cove-color-border-light)',
                    borderRadius: '8px',
                    marginBottom: '1rem'
                  }}
                >
                  <Text variant="h5">{paim.name}</Text>
                  <Text variant="bodySmall">
                    {language === 'ar' ? 'الحالة: ' : 'Status: '}{paim.status}
                  </Text>
                  <Text variant="bodySmall">
                    {language === 'ar' ? 'المنطقة: ' : 'Region: '}{paim.cultural_region}
                  </Text>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Cultural Adaptation Tab */}
      {activeTab === 'cultural' && (
        <div>
          <Text variant="h3" style={{ marginBottom: '1rem' }}>
            {language === 'ar' ? 'التكيف الثقافي' : 'Cultural Adaptation'}
          </Text>
          
          <div style={{ marginBottom: '2rem' }}>
            <Input
              value={testMessage}
              onChange={(e) => setTestMessage(e.target.value)}
              placeholder={language === 'ar' ? 'أدخل رسالة للاختبار...' : 'Enter a message to test...'}
              style={{ marginBottom: '1rem' }}
            />
            <Button 
              onClick={testMessageProcessing} 
              disabled={isLoading || !testMessage.trim()}
            >
              {language === 'ar' ? 'اختبار التكيف الثقافي' : 'Test Cultural Adaptation'}
            </Button>
          </div>

          {testResults?.messageTest && (
            <div style={{ 
              padding: '1rem', 
              border: '1px solid var(--cove-color-border-light)',
              borderRadius: '8px',
              backgroundColor: 'var(--cove-color-background-elevated)'
            }}>
              <Text variant="h4" style={{ marginBottom: '1rem' }}>
                {language === 'ar' ? 'نتائج الاختبار' : 'Test Results'}
              </Text>
              <pre style={{ 
                fontSize: '0.875rem', 
                overflow: 'auto',
                backgroundColor: 'var(--cove-color-background-paper)',
                padding: '1rem',
                borderRadius: '4px'
              }}>
                {JSON.stringify(testResults.messageTest, null, 2)}
              </pre>
            </div>
          )}
        </div>
      )}

      {/* PowerOps Tab */}
      {activeTab === 'powerops' && (
        <div>
          <Text variant="h3" style={{ marginBottom: '1rem' }}>
            {language === 'ar' ? 'إحصائيات PowerOps' : 'PowerOps Statistics'}
          </Text>
          
          {powerOpsStats ? (
            <div style={{ 
              display: 'grid', 
              gap: '1rem',
              gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))'
            }}>
              <div style={{ 
                padding: '1rem', 
                border: '1px solid var(--cove-color-border-light)',
                borderRadius: '8px'
              }}>
                <Text variant="h4">
                  {language === 'ar' ? 'مستوى المستخدم' : 'User Level'}
                </Text>
                <Text variant="h2" style={{ color: 'var(--cove-color-primary-default)' }}>
                  {userLevel}
                </Text>
              </div>
              
              <div style={{ 
                padding: '1rem', 
                border: '1px solid var(--cove-color-border-light)',
                borderRadius: '8px'
              }}>
                <Text variant="h4">
                  {language === 'ar' ? 'الخبرة' : 'Experience'}
                </Text>
                <Text variant="h2" style={{ color: 'var(--cove-color-success-default)' }}>
                  {experience}
                </Text>
              </div>
            </div>
          ) : (
            <Text variant="body">
              {language === 'ar' 
                ? 'لا توجد إحصائيات PowerOps متاحة'
                : 'No PowerOps statistics available'
              }
            </Text>
          )}
        </div>
      )}

      {/* Testing Tab */}
      {activeTab === 'testing' && (
        <div>
          <Text variant="h3" style={{ marginBottom: '1rem' }}>
            {language === 'ar' ? 'اختبار التكامل' : 'Integration Testing'}
          </Text>
          
          <div style={{ display: 'flex', gap: '1rem', marginBottom: '2rem' }}>
            <Button onClick={runConnectionTest} disabled={isLoading}>
              {language === 'ar' ? 'اختبار الاتصال' : 'Test Connection'}
            </Button>
            <Button onClick={runCulturalTest} disabled={isLoading}>
              {language === 'ar' ? 'اختبار الميزات الثقافية' : 'Test Cultural Features'}
            </Button>
          </div>

          {testResults && (
            <div style={{ 
              padding: '1rem', 
              border: '1px solid var(--cove-color-border-light)',
              borderRadius: '8px',
              backgroundColor: 'var(--cove-color-background-elevated)'
            }}>
              <Text variant="h4" style={{ marginBottom: '1rem' }}>
                {language === 'ar' ? 'نتائج الاختبار' : 'Test Results'}
              </Text>
              <pre style={{ 
                fontSize: '0.875rem', 
                overflow: 'auto',
                backgroundColor: 'var(--cove-color-background-paper)',
                padding: '1rem',
                borderRadius: '4px'
              }}>
                {JSON.stringify(testResults, null, 2)}
              </pre>
            </div>
          )}
        </div>
      )}

      {/* Loading indicator */}
      {isLoading && (
        <div style={{ 
          position: 'fixed', 
          top: '50%', 
          left: '50%', 
          transform: 'translate(-50%, -50%)',
          padding: '2rem',
          backgroundColor: 'var(--cove-color-background-elevated)',
          borderRadius: '8px',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
        }}>
          <Text variant="body">
            {language === 'ar' ? 'جاري التحميل...' : 'Loading...'}
          </Text>
        </div>
      )}
    </Container>
  );
};

export default AIgencyIntegrationDemo;
