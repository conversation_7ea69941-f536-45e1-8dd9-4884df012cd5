"""
Main startup script for COVE-AIgency Integration
Coordinates all services: PAIM Gateway, Cultural Adaptation, and Integration Service
"""

import asyncio
import logging
import os
import signal
import sys
from typing import Dict, Any
from contextlib import asynccontextmanager

import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

# Import our integration services
from .integration.paim_integration_service import create_paim_integration_service
from .api.paim_gateway import create_paim_gateway
from .cultural.adaptation_service import create_cultural_adaptation_service

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class COVEIntegrationServer:
    """Main COVE Integration Server"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.app = FastAPI(
            title="COVE-AIgency Integration Server",
            description="Unified integration server for COVE PAIM and The AIgency",
            version="1.0.0"
        )
        
        # Service instances
        self.integration_service = None
        self.gateway_service = None
        self.cultural_service = None
        
        # Server state
        self.is_running = False
        self.shutdown_event = asyncio.Event()
        
        # Setup middleware
        self._setup_middleware()
        
        # Setup signal handlers
        self._setup_signal_handlers()
    
    def _setup_middleware(self):
        """Setup CORS and other middleware"""
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=self.config.get("allowed_origins", ["*"]),
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
    
    def _setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown"""
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, initiating graceful shutdown...")
            self.shutdown_event.set()
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    async def startup(self):
        """Initialize all services"""
        try:
            logger.info("Starting COVE Integration Server...")
            
            # Initialize services
            logger.info("Initializing Integration Service...")
            self.integration_service = create_paim_integration_service(self.config)
            
            logger.info("Initializing PAIM Gateway...")
            self.gateway_service = create_paim_gateway(self.config.get("gateway", {}))
            
            logger.info("Initializing Cultural Adaptation Service...")
            self.cultural_service = create_cultural_adaptation_service(self.config.get("cultural", {}))
            
            # Mount services to main app
            self.app.mount("/integration", self.integration_service.get_app())
            self.app.mount("/gateway", self.gateway_service.get_app())
            self.app.mount("/cultural", self.cultural_service.get_app())
            
            # Add main routes
            self._setup_main_routes()
            
            # Verify service connectivity
            await self._verify_service_connectivity()
            
            self.is_running = True
            logger.info("COVE Integration Server started successfully!")
            
        except Exception as e:
            logger.error(f"Failed to start COVE Integration Server: {str(e)}")
            raise
    
    def _setup_main_routes(self):
        """Setup main application routes"""
        
        @self.app.get("/")
        async def root():
            """Root endpoint with service information"""
            return {
                "service": "COVE-AIgency Integration Server",
                "version": "1.0.0",
                "status": "running" if self.is_running else "starting",
                "services": {
                    "integration": "/integration",
                    "gateway": "/gateway", 
                    "cultural": "/cultural"
                },
                "health_check": "/health",
                "documentation": "/docs"
            }
        
        @self.app.get("/health")
        async def health_check():
            """Comprehensive health check for all services"""
            try:
                if not self.is_running:
                    return {"status": "starting", "message": "Server is starting up"}
                
                # Check integration service
                integration_status = await self.integration_service._get_integration_status()
                
                return {
                    "status": "healthy",
                    "timestamp": integration_status.dict()["timestamp"] if hasattr(integration_status, 'dict') else None,
                    "services": {
                        "integration": "healthy",
                        "gateway": integration_status.gateway_status,
                        "cultural": integration_status.cultural_service_status,
                        "aigency_connection": integration_status.aigency_connection,
                        "python_backend_connection": integration_status.python_backend_connection,
                        "redis_connection": integration_status.redis_connection
                    },
                    "active_paims": integration_status.active_paims,
                    "total_requests": integration_status.total_requests_processed
                }
                
            except Exception as e:
                logger.error(f"Health check failed: {str(e)}")
                return {
                    "status": "unhealthy",
                    "error": str(e),
                    "services": {
                        "integration": "unknown",
                        "gateway": "unknown",
                        "cultural": "unknown"
                    }
                }
        
        @self.app.get("/status")
        async def get_detailed_status():
            """Get detailed status of all services"""
            try:
                if not self.is_running:
                    return {"status": "starting", "message": "Server is starting up"}
                
                # Get comprehensive metrics
                metrics = await self.integration_service._get_comprehensive_metrics()
                
                return {
                    "server_status": "running",
                    "integration_metrics": metrics,
                    "config": {
                        "aigency_url": self.config.get("aigency_url"),
                        "python_backend_url": self.config.get("python_backend_url"),
                        "redis_url": self.config.get("redis_url")
                    }
                }
                
            except Exception as e:
                logger.error(f"Status check failed: {str(e)}")
                return {"status": "error", "error": str(e)}
    
    async def _verify_service_connectivity(self):
        """Verify connectivity to external services"""
        logger.info("Verifying service connectivity...")
        
        # Check AIgency connectivity
        try:
            import httpx
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.config.get('aigency_url', 'http://localhost:3000')}/health",
                    timeout=5.0
                )
                if response.status_code == 200:
                    logger.info("✓ AIgency service connectivity verified")
                else:
                    logger.warning(f"⚠ AIgency service returned status {response.status_code}")
        except Exception as e:
            logger.warning(f"⚠ Could not connect to AIgency service: {str(e)}")
        
        # Check Python backend connectivity
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.config.get('python_backend_url', 'http://localhost:8000')}/health",
                    timeout=5.0
                )
                if response.status_code == 200:
                    logger.info("✓ Python backend connectivity verified")
                else:
                    logger.warning(f"⚠ Python backend returned status {response.status_code}")
        except Exception as e:
            logger.warning(f"⚠ Could not connect to Python backend: {str(e)}")
        
        # Check Redis connectivity
        try:
            from redis import Redis
            redis_client = Redis.from_url(self.config.get("redis_url", "redis://localhost:6379"))
            redis_client.ping()
            logger.info("✓ Redis connectivity verified")
        except Exception as e:
            logger.warning(f"⚠ Could not connect to Redis: {str(e)}")
    
    async def shutdown(self):
        """Graceful shutdown of all services"""
        try:
            logger.info("Shutting down COVE Integration Server...")
            
            self.is_running = False
            
            # Close any active connections
            if self.gateway_service and hasattr(self.gateway_service, 'active_connections'):
                for paim_id, websocket in self.gateway_service.active_connections.items():
                    try:
                        await websocket.close()
                        logger.info(f"Closed WebSocket connection for PAIM {paim_id}")
                    except Exception as e:
                        logger.warning(f"Error closing WebSocket for PAIM {paim_id}: {str(e)}")
            
            logger.info("COVE Integration Server shutdown complete")
            
        except Exception as e:
            logger.error(f"Error during shutdown: {str(e)}")
    
    def get_app(self) -> FastAPI:
        """Get the FastAPI application instance"""
        return self.app


def load_config() -> Dict[str, Any]:
    """Load configuration from environment variables and defaults"""
    return {
        # Server configuration
        "host": os.getenv("COVE_HOST", "0.0.0.0"),
        "port": int(os.getenv("COVE_PORT", "8000")),
        "workers": int(os.getenv("COVE_WORKERS", "1")),
        
        # Service URLs
        "aigency_url": os.getenv("AIGENCY_URL", "http://localhost:3000"),
        "python_backend_url": os.getenv("PYTHON_BACKEND_URL", "http://localhost:8001"),
        "redis_url": os.getenv("REDIS_URL", "redis://localhost:6379"),
        
        # Security
        "jwt_secret": os.getenv("JWT_SECRET", "your-secret-key-change-in-production"),
        "allowed_origins": os.getenv("ALLOWED_ORIGINS", "*").split(","),
        
        # Service-specific configuration
        "gateway": {
            "jwt_secret": os.getenv("JWT_SECRET", "your-secret-key-change-in-production"),
            "redis_url": os.getenv("REDIS_URL", "redis://localhost:6379"),
            "aigency_url": os.getenv("AIGENCY_URL", "http://localhost:3000"),
            "python_backend_url": os.getenv("PYTHON_BACKEND_URL", "http://localhost:8001"),
            "allowed_origins": os.getenv("ALLOWED_ORIGINS", "*").split(",")
        },
        
        "cultural": {
            "redis_url": os.getenv("REDIS_URL", "redis://localhost:6379")
        },
        
        # Logging
        "log_level": os.getenv("LOG_LEVEL", "INFO")
    }


async def main():
    """Main entry point"""
    try:
        # Load configuration
        config = load_config()
        
        # Create server instance
        server = COVEIntegrationServer(config)
        
        # Initialize services
        await server.startup()
        
        # Create lifespan context manager
        @asynccontextmanager
        async def lifespan(app: FastAPI):
            # Startup
            yield
            # Shutdown
            await server.shutdown()
        
        # Update app with lifespan
        server.app.router.lifespan_context = lifespan
        
        # Start the server
        uvicorn_config = uvicorn.Config(
            server.get_app(),
            host=config["host"],
            port=config["port"],
            log_level=config["log_level"].lower(),
            access_log=True
        )
        
        uvicorn_server = uvicorn.Server(uvicorn_config)
        
        # Run server with graceful shutdown handling
        await uvicorn_server.serve()
        
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt, shutting down...")
    except Exception as e:
        logger.error(f"Server error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
