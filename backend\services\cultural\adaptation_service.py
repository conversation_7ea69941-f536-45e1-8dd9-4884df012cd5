"""
Cultural Adaptation Service for PAIM Integration
Handles dialect detection, cultural sensitivity, and response adaptation
"""

import asyncio
import json
import logging
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime, timezone
from dataclasses import dataclass
from enum import Enum

from fastapi import FastAPI, HTTPException
from pydantic import BaseModel, Field
import httpx
from redis import Redis
import re

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DialectType(str, Enum):
    """Arabic dialect types"""
    GULF_UAE = "gulf_uae"
    GULF_SAUDI = "gulf_saudi"
    GULF_KUWAIT = "gulf_kuwait"
    GULF_QATAR = "gulf_qatar"
    GULF_BAHRAIN = "gulf_bahrain"
    GULF_OMAN = "gulf_oman"
    LEVANTINE = "levantine"
    EGYPTIAN = "egyptian"
    MAGHREBI = "maghrebi"
    STANDARD_ARABIC = "standard_arabic"

class CulturalSensitivityLevel(str, Enum):
    """Cultural sensitivity levels"""
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"

@dataclass
class CulturalContext:
    """Cultural context for adaptation"""
    region: str
    dialect: Optional[DialectType]
    formality_level: str
    religious_considerations: bool
    business_context: bool
    gender_considerations: bool
    age_group: Optional[str]
    sensitivity_level: CulturalSensitivityLevel

class CulturalAdaptationRequest(BaseModel):
    """Request for cultural adaptation"""
    response: Dict[str, Any] = Field(..., description="Original response to adapt")
    cultural_context: Dict[str, Any] = Field(..., description="Cultural context")
    paim_id: Optional[str] = None
    user_preferences: Optional[Dict[str, Any]] = None

class CulturalAdaptationResponse(BaseModel):
    """Response from cultural adaptation"""
    adapted_content: Optional[Dict[str, Any]] = None
    adaptation_metadata: Dict[str, Any] = Field(default_factory=dict)
    confidence_score: float = Field(default=0.0, ge=0.0, le=1.0)
    dialect_detected: Optional[str] = None
    cultural_appropriateness_score: float = Field(default=0.0, ge=0.0, le=1.0)

class DialectDetectionRequest(BaseModel):
    """Request for dialect detection"""
    text: str = Field(..., description="Text to analyze")
    context: Optional[Dict[str, Any]] = None

class DialectDetectionResponse(BaseModel):
    """Response from dialect detection"""
    detected_dialect: DialectType
    confidence: float = Field(ge=0.0, le=1.0)
    alternative_dialects: List[Tuple[DialectType, float]] = Field(default_factory=list)
    linguistic_features: Dict[str, Any] = Field(default_factory=dict)

class CulturalAdaptationService:
    """Main cultural adaptation service"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.app = FastAPI(title="Cultural Adaptation Service", version="1.0.0")
        self.redis_client = Redis.from_url(config.get("redis_url", "redis://localhost:6379"))
        
        # Load dialect patterns and cultural rules
        self.dialect_patterns = self._load_dialect_patterns()
        self.cultural_rules = self._load_cultural_rules()
        self.formality_markers = self._load_formality_markers()
        
        # Setup routes
        self._setup_routes()
    
    def _setup_routes(self):
        """Setup API routes"""
        
        @self.app.post("/api/v1/cultural/adapt", response_model=CulturalAdaptationResponse)
        async def adapt_response(request: CulturalAdaptationRequest):
            """Adapt response for cultural context"""
            try:
                # Parse cultural context
                context = self._parse_cultural_context(request.cultural_context)
                
                # Detect dialect if text content is present
                dialect_info = None
                if self._has_arabic_text(request.response):
                    dialect_info = await self._detect_dialect_in_response(request.response)
                
                # Apply cultural adaptations
                adapted_content = await self._apply_cultural_adaptations(
                    request.response, context, dialect_info
                )
                
                # Calculate appropriateness score
                appropriateness_score = self._calculate_appropriateness_score(
                    adapted_content, context
                )
                
                # Store adaptation for learning
                await self._store_adaptation_data(request, adapted_content, context)
                
                return CulturalAdaptationResponse(
                    adapted_content=adapted_content,
                    adaptation_metadata={
                        "adaptations_applied": self._get_applied_adaptations(adapted_content, request.response),
                        "cultural_context": context.__dict__,
                        "timestamp": datetime.now(timezone.utc).isoformat()
                    },
                    confidence_score=0.85,  # Would be calculated based on adaptation quality
                    dialect_detected=dialect_info.detected_dialect.value if dialect_info else None,
                    cultural_appropriateness_score=appropriateness_score
                )
                
            except Exception as e:
                logger.error(f"Error in cultural adaptation: {str(e)}")
                raise HTTPException(status_code=500, detail=f"Adaptation failed: {str(e)}")
        
        @self.app.post("/api/v1/cultural/detect-dialect", response_model=DialectDetectionResponse)
        async def detect_dialect(request: DialectDetectionRequest):
            """Detect Arabic dialect in text"""
            try:
                detection_result = await self._detect_dialect(request.text, request.context)
                return detection_result
                
            except Exception as e:
                logger.error(f"Error in dialect detection: {str(e)}")
                raise HTTPException(status_code=500, detail=f"Dialect detection failed: {str(e)}")
        
        @self.app.post("/api/v1/cultural/learn")
        async def learn_from_interaction(data: Dict[str, Any]):
            """Learn from user interactions for cultural adaptation"""
            try:
                paim_id = data.get("paim_id")
                learning_data = data.get("learning_data", {})
                
                # Process learning data
                await self._process_cultural_learning(paim_id, learning_data)
                
                return {
                    "success": True,
                    "message": "Cultural learning data processed",
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }
                
            except Exception as e:
                logger.error(f"Error in cultural learning: {str(e)}")
                raise HTTPException(status_code=500, detail=f"Cultural learning failed: {str(e)}")
        
        @self.app.get("/health")
        async def health_check():
            """Health check endpoint"""
            return {
                "status": "healthy",
                "service": "cultural_adaptation",
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
    
    def _parse_cultural_context(self, context_data: Dict[str, Any]) -> CulturalContext:
        """Parse cultural context from request data"""
        return CulturalContext(
            region=context_data.get("region", "uae"),
            dialect=DialectType(context_data.get("dialect", "gulf_uae")) if context_data.get("dialect") else None,
            formality_level=context_data.get("formality_level", "medium"),
            religious_considerations=context_data.get("religious_considerations", True),
            business_context=context_data.get("business_context", False),
            gender_considerations=context_data.get("gender_considerations", True),
            age_group=context_data.get("age_group"),
            sensitivity_level=CulturalSensitivityLevel(context_data.get("sensitivity_level", "high"))
        )
    
    def _has_arabic_text(self, response: Dict[str, Any]) -> bool:
        """Check if response contains Arabic text"""
        def check_text(obj):
            if isinstance(obj, str):
                return bool(re.search(r'[\u0600-\u06FF]', obj))
            elif isinstance(obj, dict):
                return any(check_text(v) for v in obj.values())
            elif isinstance(obj, list):
                return any(check_text(item) for item in obj)
            return False
        
        return check_text(response)
    
    async def _detect_dialect_in_response(self, response: Dict[str, Any]) -> Optional[DialectDetectionResponse]:
        """Detect dialect in response content"""
        # Extract Arabic text from response
        arabic_texts = self._extract_arabic_text(response)
        
        if not arabic_texts:
            return None
        
        # Combine texts for analysis
        combined_text = " ".join(arabic_texts)
        
        # Perform dialect detection
        return await self._detect_dialect(combined_text)
    
    def _extract_arabic_text(self, obj) -> List[str]:
        """Extract Arabic text from nested object"""
        arabic_texts = []
        
        def extract_recursive(item):
            if isinstance(item, str):
                if re.search(r'[\u0600-\u06FF]', item):
                    arabic_texts.append(item)
            elif isinstance(item, dict):
                for value in item.values():
                    extract_recursive(value)
            elif isinstance(item, list):
                for element in item:
                    extract_recursive(element)
        
        extract_recursive(obj)
        return arabic_texts
    
    async def _detect_dialect(self, text: str, context: Optional[Dict[str, Any]] = None) -> DialectDetectionResponse:
        """Detect Arabic dialect using pattern matching and ML"""
        # Simple pattern-based detection (would be enhanced with ML models)
        dialect_scores = {}
        
        for dialect, patterns in self.dialect_patterns.items():
            score = 0
            for pattern in patterns:
                matches = len(re.findall(pattern, text, re.IGNORECASE))
                score += matches * patterns[pattern].get("weight", 1)
            
            if score > 0:
                dialect_scores[dialect] = score / len(text.split())
        
        if not dialect_scores:
            # Default to Gulf UAE if no patterns match
            return DialectDetectionResponse(
                detected_dialect=DialectType.GULF_UAE,
                confidence=0.3,
                alternative_dialects=[],
                linguistic_features={}
            )
        
        # Sort by score
        sorted_dialects = sorted(dialect_scores.items(), key=lambda x: x[1], reverse=True)
        best_dialect = sorted_dialects[0]
        
        # Normalize confidence score
        max_score = best_dialect[1]
        confidence = min(max_score, 1.0)
        
        # Get alternatives
        alternatives = [
            (DialectType(dialect), score) 
            for dialect, score in sorted_dialects[1:3]
        ]
        
        return DialectDetectionResponse(
            detected_dialect=DialectType(best_dialect[0]),
            confidence=confidence,
            alternative_dialects=alternatives,
            linguistic_features=self._extract_linguistic_features(text)
        )
    
    def _extract_linguistic_features(self, text: str) -> Dict[str, Any]:
        """Extract linguistic features from Arabic text"""
        features = {
            "word_count": len(text.split()),
            "has_formal_markers": bool(re.search(r'(إن|أن|لكن|غير|سوف)', text)),
            "has_informal_markers": bool(re.search(r'(شو|وين|شلون|هاي)', text)),
            "question_markers": len(re.findall(r'[؟?]', text)),
            "exclamation_markers": len(re.findall(r'[!]', text))
        }
        
        return features

    async def _apply_cultural_adaptations(
        self,
        response: Dict[str, Any],
        context: CulturalContext,
        dialect_info: Optional[DialectDetectionResponse]
    ) -> Dict[str, Any]:
        """Apply cultural adaptations to response"""
        adapted_response = response.copy()

        # Apply formality adjustments
        if context.formality_level in ["high", "formal"]:
            adapted_response = self._apply_formal_adaptations(adapted_response)
        elif context.formality_level in ["low", "informal"]:
            adapted_response = self._apply_informal_adaptations(adapted_response)

        # Apply religious considerations
        if context.religious_considerations:
            adapted_response = self._apply_religious_adaptations(adapted_response)

        # Apply gender considerations
        if context.gender_considerations:
            adapted_response = self._apply_gender_adaptations(adapted_response, context)

        # Apply business context adaptations
        if context.business_context:
            adapted_response = self._apply_business_adaptations(adapted_response)

        # Apply dialect-specific adaptations
        if dialect_info and dialect_info.detected_dialect:
            adapted_response = self._apply_dialect_adaptations(adapted_response, dialect_info.detected_dialect)

        return adapted_response

    def _apply_formal_adaptations(self, response: Dict[str, Any]) -> Dict[str, Any]:
        """Apply formal language adaptations"""
        def formalize_text(text: str) -> str:
            if not isinstance(text, str):
                return text

            # Replace informal greetings with formal ones
            formal_replacements = {
                r'\bهلا\b': 'أهلاً وسهلاً',
                r'\bشلونك\b': 'كيف حالك',
                r'\bوين\b': 'أين',
                r'\bشو\b': 'ماذا',
                r'\bهاي\b': 'هذه'
            }

            for pattern, replacement in formal_replacements.items():
                text = re.sub(pattern, replacement, text)

            return text

        return self._apply_text_transformation(response, formalize_text)

    def _apply_informal_adaptations(self, response: Dict[str, Any]) -> Dict[str, Any]:
        """Apply informal language adaptations"""
        def informalize_text(text: str) -> str:
            if not isinstance(text, str):
                return text

            # Replace formal expressions with informal ones
            informal_replacements = {
                r'\bأهلاً وسهلاً\b': 'هلا',
                r'\bكيف حالك\b': 'شلونك',
                r'\bأين\b': 'وين',
                r'\bماذا\b': 'شو'
            }

            for pattern, replacement in informal_replacements.items():
                text = re.sub(pattern, replacement, text)

            return text

        return self._apply_text_transformation(response, informalize_text)

    def _apply_religious_adaptations(self, response: Dict[str, Any]) -> Dict[str, Any]:
        """Apply religious sensitivity adaptations"""
        def add_religious_context(text: str) -> str:
            if not isinstance(text, str):
                return text

            # Add appropriate religious expressions
            if re.search(r'(نجح|تم|انتهى|اكتمل)', text):
                text = text + ' بإذن الله'

            # Replace potentially sensitive terms
            religious_replacements = {
                r'\bحظ\b': 'توفيق',
                r'\bحظاً سعيداً\b': 'بالتوفيق',
                r'\bصدفة\b': 'قدر'
            }

            for pattern, replacement in religious_replacements.items():
                text = re.sub(pattern, replacement, text)

            return text

        return self._apply_text_transformation(response, add_religious_context)

    def _apply_gender_adaptations(self, response: Dict[str, Any], context: CulturalContext) -> Dict[str, Any]:
        """Apply gender-sensitive adaptations"""
        def adapt_gender_language(text: str) -> str:
            if not isinstance(text, str):
                return text

            # Use gender-neutral language where appropriate
            gender_neutral_replacements = {
                r'\bالرجل\b': 'الشخص',
                r'\bالمرأة\b': 'الشخص',
                r'\bالرجال\b': 'الأشخاص',
                r'\bالنساء\b': 'الأشخاص'
            }

            for pattern, replacement in gender_neutral_replacements.items():
                text = re.sub(pattern, replacement, text)

            return text

        return self._apply_text_transformation(response, adapt_gender_language)

    def _apply_business_adaptations(self, response: Dict[str, Any]) -> Dict[str, Any]:
        """Apply business context adaptations"""
        def adapt_business_language(text: str) -> str:
            if not isinstance(text, str):
                return text

            # Use professional terminology
            business_replacements = {
                r'\bشغل\b': 'عمل',
                r'\bدوام\b': 'ساعات العمل',
                r'\bمدير\b': 'مدير تنفيذي',
                r'\bشركة\b': 'مؤسسة'
            }

            for pattern, replacement in business_replacements.items():
                text = re.sub(pattern, replacement, text)

            return text

        return self._apply_text_transformation(response, adapt_business_language)

    def _apply_dialect_adaptations(self, response: Dict[str, Any], dialect: DialectType) -> Dict[str, Any]:
        """Apply dialect-specific adaptations"""
        dialect_adaptations = {
            DialectType.GULF_UAE: {
                r'\bكيف\b': 'شلون',
                r'\bأين\b': 'وين',
                r'\bماذا\b': 'شو'
            },
            DialectType.GULF_SAUDI: {
                r'\bكيف\b': 'كيف',
                r'\bأين\b': 'وين',
                r'\bماذا\b': 'ايش'
            },
            DialectType.LEVANTINE: {
                r'\bكيف\b': 'كيف',
                r'\bأين\b': 'وين',
                r'\bماذا\b': 'شو'
            }
        }

        adaptations = dialect_adaptations.get(dialect, {})

        def adapt_dialect(text: str) -> str:
            if not isinstance(text, str):
                return text

            for pattern, replacement in adaptations.items():
                text = re.sub(pattern, replacement, text)

            return text

        return self._apply_text_transformation(response, adapt_dialect)

    def _apply_text_transformation(self, obj: Any, transform_func) -> Any:
        """Apply text transformation recursively to nested objects"""
        if isinstance(obj, str):
            return transform_func(obj)
        elif isinstance(obj, dict):
            return {key: self._apply_text_transformation(value, transform_func) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._apply_text_transformation(item, transform_func) for item in obj]
        else:
            return obj

    def _calculate_appropriateness_score(self, adapted_content: Dict[str, Any], context: CulturalContext) -> float:
        """Calculate cultural appropriateness score"""
        score = 0.8  # Base score

        # Check for cultural markers
        if self._has_appropriate_formality(adapted_content, context.formality_level):
            score += 0.1

        if context.religious_considerations and self._has_religious_sensitivity(adapted_content):
            score += 0.05

        if context.business_context and self._has_professional_language(adapted_content):
            score += 0.05

        return min(score, 1.0)

    def _has_appropriate_formality(self, content: Dict[str, Any], formality_level: str) -> bool:
        """Check if content has appropriate formality level"""
        # Simple check for formal/informal markers
        text_content = self._extract_text_content(content)

        if formality_level in ["high", "formal"]:
            return bool(re.search(r'(أهلاً وسهلاً|كيف حالك|تفضل)', text_content))
        elif formality_level in ["low", "informal"]:
            return bool(re.search(r'(هلا|شلونك|تعال)', text_content))

        return True

    def _has_religious_sensitivity(self, content: Dict[str, Any]) -> bool:
        """Check if content shows religious sensitivity"""
        text_content = self._extract_text_content(content)
        return bool(re.search(r'(بإذن الله|إن شاء الله|بالتوفيق)', text_content))

    def _has_professional_language(self, content: Dict[str, Any]) -> bool:
        """Check if content uses professional language"""
        text_content = self._extract_text_content(content)
        return bool(re.search(r'(مؤسسة|عمل|مدير تنفيذي|ساعات العمل)', text_content))

    def _extract_text_content(self, obj: Any) -> str:
        """Extract all text content from nested object"""
        texts = []

        def extract_recursive(item):
            if isinstance(item, str):
                texts.append(item)
            elif isinstance(item, dict):
                for value in item.values():
                    extract_recursive(value)
            elif isinstance(item, list):
                for element in item:
                    extract_recursive(element)

        extract_recursive(obj)
        return " ".join(texts)

    def _get_applied_adaptations(self, adapted_content: Dict[str, Any], original_content: Dict[str, Any]) -> List[str]:
        """Get list of adaptations that were applied"""
        adaptations = []

        # Compare original and adapted content to identify changes
        original_text = self._extract_text_content(original_content)
        adapted_text = self._extract_text_content(adapted_content)

        if original_text != adapted_text:
            if re.search(r'أهلاً وسهلاً', adapted_text) and not re.search(r'أهلاً وسهلاً', original_text):
                adaptations.append("formality_increase")

            if re.search(r'بإذن الله', adapted_text) and not re.search(r'بإذن الله', original_text):
                adaptations.append("religious_sensitivity")

            if re.search(r'مؤسسة', adapted_text) and not re.search(r'مؤسسة', original_text):
                adaptations.append("business_language")

        return adaptations

    async def _store_adaptation_data(
        self,
        request: CulturalAdaptationRequest,
        adapted_content: Dict[str, Any],
        context: CulturalContext
    ):
        """Store adaptation data for learning purposes"""
        try:
            adaptation_data = {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "paim_id": request.paim_id,
                "original_content": request.response,
                "adapted_content": adapted_content,
                "cultural_context": context.__dict__,
                "adaptations_applied": self._get_applied_adaptations(adapted_content, request.response)
            }

            # Store in Redis for learning
            key = f"adaptation_data:{request.paim_id}:{datetime.now(timezone.utc).timestamp()}"
            self.redis_client.setex(key, 604800, json.dumps(adaptation_data))  # 7 days TTL

        except Exception as e:
            logger.error(f"Failed to store adaptation data: {str(e)}")

    async def _process_cultural_learning(self, paim_id: str, learning_data: Dict[str, Any]):
        """Process cultural learning data"""
        try:
            # Store learning feedback
            learning_entry = {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "paim_id": paim_id,
                "feedback_type": learning_data.get("type", "general"),
                "user_satisfaction": learning_data.get("satisfaction_score"),
                "cultural_accuracy": learning_data.get("cultural_accuracy"),
                "suggestions": learning_data.get("suggestions", [])
            }

            # Store in Redis
            key = f"cultural_learning:{paim_id}:{datetime.now(timezone.utc).timestamp()}"
            self.redis_client.setex(key, 2592000, json.dumps(learning_entry))  # 30 days TTL

            # Update adaptation patterns based on feedback
            await self._update_adaptation_patterns(paim_id, learning_data)

        except Exception as e:
            logger.error(f"Failed to process cultural learning: {str(e)}")

    async def _update_adaptation_patterns(self, paim_id: str, learning_data: Dict[str, Any]):
        """Update adaptation patterns based on user feedback"""
        try:
            # Get current patterns for this PAIM
            patterns_key = f"adaptation_patterns:{paim_id}"
            current_patterns = self.redis_client.get(patterns_key)

            if current_patterns:
                patterns = json.loads(current_patterns)
            else:
                patterns = {"formality_preferences": {}, "dialect_preferences": {}, "cultural_preferences": {}}

            # Update patterns based on feedback
            if learning_data.get("formality_feedback"):
                patterns["formality_preferences"].update(learning_data["formality_feedback"])

            if learning_data.get("dialect_feedback"):
                patterns["dialect_preferences"].update(learning_data["dialect_feedback"])

            if learning_data.get("cultural_feedback"):
                patterns["cultural_preferences"].update(learning_data["cultural_feedback"])

            # Store updated patterns
            self.redis_client.setex(patterns_key, 7776000, json.dumps(patterns))  # 90 days TTL

        except Exception as e:
            logger.error(f"Failed to update adaptation patterns: {str(e)}")

    def _load_dialect_patterns(self) -> Dict[str, Dict[str, Dict[str, Any]]]:
        """Load dialect detection patterns"""
        return {
            "gulf_uae": {
                r'\bشلون\b': {"weight": 3},
                r'\bوين\b': {"weight": 2},
                r'\bشو\b': {"weight": 2},
                r'\bهاي\b': {"weight": 2},
                r'\bيالله\b': {"weight": 1}
            },
            "gulf_saudi": {
                r'\bايش\b': {"weight": 3},
                r'\bوين\b': {"weight": 2},
                r'\bكيف\b': {"weight": 1},
                r'\bيلا\b': {"weight": 1}
            },
            "levantine": {
                r'\bشو\b': {"weight": 3},
                r'\bوين\b': {"weight": 2},
                r'\bكيف\b': {"weight": 2},
                r'\bهيك\b': {"weight": 2}
            }
        }

    def _load_cultural_rules(self) -> Dict[str, Any]:
        """Load cultural adaptation rules"""
        return {
            "religious_sensitivity": {
                "add_inshallah": ["future_events", "plans", "hopes"],
                "add_mashallah": ["achievements", "compliments"],
                "avoid_terms": ["luck", "chance", "random"]
            },
            "formality_levels": {
                "high": ["business", "elderly", "authority"],
                "medium": ["peers", "colleagues"],
                "low": ["friends", "family", "informal"]
            }
        }

    def _load_formality_markers(self) -> Dict[str, List[str]]:
        """Load formality markers for different levels"""
        return {
            "formal": ["أهلاً وسهلاً", "كيف حالك", "تفضل", "من فضلك"],
            "informal": ["هلا", "شلونك", "تعال", "يالله"]
        }

    def get_app(self) -> FastAPI:
        """Get the FastAPI application instance"""
        return self.app


# Factory function to create cultural adaptation service
def create_cultural_adaptation_service(config: Dict[str, Any]) -> CulturalAdaptationService:
    """Create and configure Cultural Adaptation Service instance"""
    return CulturalAdaptationService(config)
