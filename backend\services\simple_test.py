"""
Simple COVE Integration Test Script
Tests basic connectivity and service functionality
"""

import asyncio
import logging
from typing import Dict, Any

import httpx

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SimpleCOVETester:
    """Simple test suite for COVE integration"""
    
    def __init__(self):
        self.cove_base_url = "http://localhost:8000"
        self.aigency_base_url = "http://localhost:3000"
        self.test_results = []
    
    async def run_all_tests(self):
        """Run all basic tests"""
        logger.info("Starting Simple COVE Integration Tests...")
        
        tests = [
            ("Service Health Check", self.test_service_health),
            ("Basic Endpoints", self.test_basic_endpoints),
            ("AIgency Connectivity", self.test_aigency_connectivity),
            ("Cultural Features", self.test_cultural_features)
        ]
        
        for test_name, test_func in tests:
            logger.info(f"\n{'='*50}")
            logger.info(f"Running Test: {test_name}")
            logger.info(f"{'='*50}")
            
            try:
                result = await test_func()
                self.test_results.append({
                    "test": test_name,
                    "status": "PASSED" if result else "FAILED",
                    "details": result
                })
                logger.info(f"✅ {test_name}: {'PASSED' if result else 'FAILED'}")
            except Exception as e:
                self.test_results.append({
                    "test": test_name,
                    "status": "ERROR",
                    "error": str(e)
                })
                logger.error(f"❌ {test_name}: ERROR - {str(e)}")
        
        # Print summary
        self.print_test_summary()
    
    async def test_service_health(self) -> bool:
        """Test service health check"""
        try:
            async with httpx.AsyncClient() as client:
                logger.info("Testing COVE service health...")
                response = await client.get(f"{self.cove_base_url}/health", timeout=10.0)
                
                if response.status_code == 200:
                    data = response.json()
                    logger.info(f"Service Status: {data.get('status')}")
                    logger.info(f"Service Name: {data.get('service')}")
                    return data.get('status') == 'healthy'
                else:
                    logger.error(f"Health check failed: {response.status_code}")
                    return False
                    
        except Exception as e:
            logger.error(f"Health check error: {str(e)}")
            return False
    
    async def test_basic_endpoints(self) -> bool:
        """Test basic service endpoints"""
        try:
            async with httpx.AsyncClient() as client:
                # Test root endpoint
                logger.info("Testing root endpoint...")
                root_response = await client.get(f"{self.cove_base_url}/", timeout=10.0)
                
                if root_response.status_code != 200:
                    logger.error(f"Root endpoint failed: {root_response.status_code}")
                    return False
                
                root_data = root_response.json()
                logger.info(f"Service: {root_data.get('service')}")
                logger.info(f"Version: {root_data.get('version')}")
                
                # Test test endpoint
                logger.info("Testing test endpoint...")
                test_response = await client.get(f"{self.cove_base_url}/test", timeout=10.0)
                
                if test_response.status_code != 200:
                    logger.error(f"Test endpoint failed: {test_response.status_code}")
                    return False
                
                test_data = test_response.json()
                logger.info(f"Test Message: {test_data.get('message')}")
                
                return test_data.get('test_passed', False)
                
        except Exception as e:
            logger.error(f"Basic endpoints test error: {str(e)}")
            return False
    
    async def test_aigency_connectivity(self) -> bool:
        """Test AIgency connectivity through COVE service"""
        try:
            async with httpx.AsyncClient() as client:
                logger.info("Testing AIgency connectivity...")
                response = await client.post(f"{self.cove_base_url}/api/v1/test/aigency", timeout=15.0)
                
                if response.status_code == 200:
                    data = response.json()
                    aigency_status = data.get('aigency_status')
                    logger.info(f"AIgency Status: {aigency_status}")
                    logger.info(f"AIgency URL: {data.get('aigency_url')}")
                    logger.info(f"Message: {data.get('message')}")
                    
                    return aigency_status == 'connected'
                else:
                    logger.error(f"AIgency connectivity test failed: {response.status_code}")
                    return False
                    
        except Exception as e:
            logger.error(f"AIgency connectivity test error: {str(e)}")
            return False
    
    async def test_cultural_features(self) -> bool:
        """Test cultural adaptation features"""
        try:
            async with httpx.AsyncClient() as client:
                logger.info("Testing cultural features...")
                response = await client.post(f"{self.cove_base_url}/api/v1/test/cultural", timeout=10.0)
                
                if response.status_code == 200:
                    data = response.json()
                    logger.info(f"Cultural Message: {data.get('message')}")
                    
                    supported_dialects = data.get('supported_dialects', [])
                    features = data.get('features', [])
                    
                    logger.info(f"Supported Dialects: {', '.join(supported_dialects)}")
                    logger.info(f"Available Features: {', '.join(features)}")
                    
                    # Check if we have expected dialects and features
                    expected_dialects = ['gulf_uae', 'gulf_saudi', 'levantine']
                    expected_features = ['dialect_detection', 'formality_adaptation']
                    
                    has_dialects = all(dialect in supported_dialects for dialect in expected_dialects)
                    has_features = all(feature in features for feature in expected_features)
                    
                    return has_dialects and has_features
                else:
                    logger.error(f"Cultural features test failed: {response.status_code}")
                    return False
                    
        except Exception as e:
            logger.error(f"Cultural features test error: {str(e)}")
            return False
    
    def print_test_summary(self):
        """Print test summary"""
        logger.info(f"\n{'='*60}")
        logger.info("Simple COVE Integration Test Summary")
        logger.info(f"{'='*60}")
        
        passed = sum(1 for result in self.test_results if result["status"] == "PASSED")
        failed = sum(1 for result in self.test_results if result["status"] == "FAILED")
        errors = sum(1 for result in self.test_results if result["status"] == "ERROR")
        
        logger.info(f"Total Tests: {len(self.test_results)}")
        logger.info(f"✅ Passed: {passed}")
        logger.info(f"❌ Failed: {failed}")
        logger.info(f"🔥 Errors: {errors}")
        
        if failed == 0 and errors == 0:
            logger.info("\n🎉 ALL TESTS PASSED! Basic COVE service is working correctly.")
            logger.info("\nNext steps:")
            logger.info("1. Ensure The AIgency backend is running on localhost:3000")
            logger.info("2. Start Redis server for full integration")
            logger.info("3. Run the full integration test suite")
        else:
            logger.info("\n⚠️  Some tests failed. Please check the logs above for details.")
            
            if any(result["status"] == "FAILED" and "aigency" in result["test"].lower() for result in self.test_results):
                logger.info("\n💡 AIgency connectivity failed. To fix this:")
                logger.info("   - Start The AIgency backend: cd /path/to/theaigency && npm start")
                logger.info("   - Ensure it's running on http://localhost:3000")
        
        logger.info(f"\n{'='*60}")

async def main():
    """Main test runner"""
    logger.info("🚀 Starting Simple COVE Integration Tests")
    logger.info("This will test basic service functionality and connectivity")
    logger.info("Make sure the COVE service is running: python simple_start.py")
    logger.info("")
    
    tester = SimpleCOVETester()
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
