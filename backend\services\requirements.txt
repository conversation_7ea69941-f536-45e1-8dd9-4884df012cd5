# COVE-AIgency Integration Services Requirements

# Core FastAPI and async support
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
python-multipart==0.0.6

# HTTP client for service communication
httpx==0.25.2

# Redis for caching and session management
redis==5.0.1

# JWT for authentication
PyJWT==2.8.0

# WebSocket support
websockets==12.0

# JSON handling
orjson==3.9.10

# Environment variable management
python-dotenv==1.0.0

# Logging and monitoring
structlog==23.2.0

# Arabic text processing and cultural adaptation
arabic-reshaper==3.0.0
python-bidi==0.4.2

# Regular expressions for dialect detection
regex==2023.10.3

# Date and time handling
python-dateutil==2.8.2

# Background task processing
celery==5.3.4

# Database connectivity (if needed)
asyncpg==0.29.0
sqlalchemy[asyncio]==2.0.23

# Testing dependencies
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-mock==3.12.0
httpx==0.25.2  # For testing HTTP clients

# Development dependencies
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# Security
cryptography==41.0.8
passlib[bcrypt]==1.7.4

# Configuration management
pyyaml==6.0.1
toml==0.10.2

# Monitoring and metrics
prometheus-client==0.19.0

# Additional utilities
click==8.1.7
rich==13.7.0
typer==0.9.0
