"""
The AIgency Backend Client for COVE Integration
Handles communication with The AIgency production backend
"""

import asyncio
import json
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timezone
from dataclasses import dataclass

import httpx
from pydantic import BaseModel, Field

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AIgencyAuthResponse(BaseModel):
    """Response from AIgency authentication"""
    success: bool
    data: Optional[Dict[str, Any]] = None
    error: Optional[Dict[str, Any]] = None

class AIgencyPAIMResponse(BaseModel):
    """Response from AIgency PAIM operations"""
    success: bool
    data: Optional[Dict[str, Any]] = None
    error: Optional[Dict[str, Any]] = None

class AIgencyAgentResponse(BaseModel):
    """Response from AIgency agent operations"""
    success: bool
    data: Optional[Dict[str, Any]] = None
    error: Optional[Dict[str, Any]] = None

@dataclass
class AIgencyConfig:
    """Configuration for AIgency client"""
    base_url: str = "http://localhost:3000"
    timeout: int = 30
    max_retries: int = 3
    retry_delay: float = 1.0

class AIgencyClient:
    """Client for The AIgency Backend API"""
    
    def __init__(self, config: AIgencyConfig):
        self.config = config
        self.base_url = config.base_url
        self.timeout = config.timeout
        self.max_retries = config.max_retries
        self.retry_delay = config.retry_delay
        
        # Authentication state
        self.access_token: Optional[str] = None
        self.refresh_token: Optional[str] = None
        self.user_id: Optional[str] = None
        
        # HTTP client
        self.client = httpx.AsyncClient(
            base_url=self.base_url,
            timeout=self.timeout
        )
    
    async def __aenter__(self):
        """Async context manager entry"""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close()
    
    async def close(self):
        """Close the HTTP client"""
        await self.client.aclose()
    
    # Authentication Methods
    
    async def register_cove_user(
        self, 
        email: str, 
        password: str, 
        first_name: str = "COVE", 
        last_name: str = "PAIM",
        organization_id: str = "cove_org"
    ) -> AIgencyAuthResponse:
        """Register a COVE user with The AIgency"""
        try:
            response = await self.client.post(
                "/api/v1/auth/register",
                json={
                    "email": email,
                    "password": password,
                    "firstName": first_name,
                    "lastName": last_name,
                    "organizationId": organization_id
                }
            )
            
            if response.status_code == 200 or response.status_code == 201:
                data = response.json()
                if data.get("success"):
                    # Store authentication tokens
                    user_data = data.get("data", {})
                    self.access_token = user_data.get("token")
                    self.refresh_token = user_data.get("refreshToken")
                    self.user_id = user_data.get("user", {}).get("id")
                    
                    logger.info(f"Successfully registered COVE user: {email}")
                    return AIgencyAuthResponse(success=True, data=data)
                else:
                    return AIgencyAuthResponse(success=False, error=data.get("error"))
            else:
                error_data = response.json() if response.content else {"message": "Registration failed"}
                return AIgencyAuthResponse(success=False, error=error_data)
                
        except Exception as e:
            logger.error(f"Error registering COVE user: {str(e)}")
            return AIgencyAuthResponse(success=False, error={"message": str(e)})
    
    async def login(self, email: str, password: str) -> AIgencyAuthResponse:
        """Login to The AIgency"""
        try:
            response = await self.client.post(
                "/api/v1/auth/login",
                json={
                    "email": email,
                    "password": password
                }
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    # Store authentication tokens
                    user_data = data.get("data", {})
                    self.access_token = user_data.get("token")
                    self.refresh_token = user_data.get("refreshToken")
                    self.user_id = user_data.get("user", {}).get("id")
                    
                    logger.info(f"Successfully logged in: {email}")
                    return AIgencyAuthResponse(success=True, data=data)
                else:
                    return AIgencyAuthResponse(success=False, error=data.get("error"))
            else:
                error_data = response.json() if response.content else {"message": "Login failed"}
                return AIgencyAuthResponse(success=False, error=error_data)
                
        except Exception as e:
            logger.error(f"Error logging in: {str(e)}")
            return AIgencyAuthResponse(success=False, error={"message": str(e)})
    
    async def refresh_access_token(self) -> AIgencyAuthResponse:
        """Refresh the access token"""
        if not self.refresh_token:
            return AIgencyAuthResponse(success=False, error={"message": "No refresh token available"})
        
        try:
            response = await self.client.post(
                "/api/v1/auth/refresh",
                headers={"Authorization": f"Bearer {self.refresh_token}"}
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    # Update access token
                    self.access_token = data.get("data", {}).get("token")
                    logger.info("Successfully refreshed access token")
                    return AIgencyAuthResponse(success=True, data=data)
                else:
                    return AIgencyAuthResponse(success=False, error=data.get("error"))
            else:
                error_data = response.json() if response.content else {"message": "Token refresh failed"}
                return AIgencyAuthResponse(success=False, error=error_data)
                
        except Exception as e:
            logger.error(f"Error refreshing token: {str(e)}")
            return AIgencyAuthResponse(success=False, error={"message": str(e)})
    
    def _get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers"""
        if not self.access_token:
            raise ValueError("No access token available. Please login first.")
        
        return {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json"
        }
    
    # PAIM Management Methods
    
    async def create_paim(
        self,
        name: str,
        description: str,
        paim_type: str = "customer_service",
        capabilities: List[str] = None,
        configuration: Dict[str, Any] = None
    ) -> AIgencyPAIMResponse:
        """Create a PAIM instance in The AIgency"""
        if capabilities is None:
            capabilities = ["chat", "email", "knowledge_base", "cultural_adaptation"]
        
        if configuration is None:
            configuration = {
                "model": "gpt-4",
                "temperature": 0.7,
                "maxTokens": 2048,
                "coveIntegration": True,
                "culturalAdaptation": True,
                "arabicDialectSupport": True
            }
        
        try:
            response = await self.client.post(
                "/api/v1/paim",
                headers=self._get_auth_headers(),
                json={
                    "name": name,
                    "description": description,
                    "type": paim_type,
                    "capabilities": capabilities,
                    "configuration": configuration
                }
            )
            
            if response.status_code == 200 or response.status_code == 201:
                data = response.json()
                if data.get("success"):
                    logger.info(f"Successfully created PAIM: {name}")
                    return AIgencyPAIMResponse(success=True, data=data)
                else:
                    return AIgencyPAIMResponse(success=False, error=data.get("error"))
            else:
                error_data = response.json() if response.content else {"message": "PAIM creation failed"}
                return AIgencyPAIMResponse(success=False, error=error_data)
                
        except Exception as e:
            logger.error(f"Error creating PAIM: {str(e)}")
            return AIgencyPAIMResponse(success=False, error={"message": str(e)})
    
    async def get_paim(self, paim_id: str) -> AIgencyPAIMResponse:
        """Get PAIM details"""
        try:
            response = await self.client.get(
                f"/api/v1/paim/{paim_id}",
                headers=self._get_auth_headers()
            )
            
            if response.status_code == 200:
                data = response.json()
                return AIgencyPAIMResponse(success=True, data=data)
            else:
                error_data = response.json() if response.content else {"message": "PAIM not found"}
                return AIgencyPAIMResponse(success=False, error=error_data)
                
        except Exception as e:
            logger.error(f"Error getting PAIM: {str(e)}")
            return AIgencyPAIMResponse(success=False, error={"message": str(e)})
    
    async def activate_paim(self, paim_id: str) -> AIgencyPAIMResponse:
        """Activate a PAIM instance"""
        try:
            response = await self.client.post(
                f"/api/v1/paim/{paim_id}/activate",
                headers=self._get_auth_headers()
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    logger.info(f"Successfully activated PAIM: {paim_id}")
                    return AIgencyPAIMResponse(success=True, data=data)
                else:
                    return AIgencyPAIMResponse(success=False, error=data.get("error"))
            else:
                error_data = response.json() if response.content else {"message": "PAIM activation failed"}
                return AIgencyPAIMResponse(success=False, error=error_data)
                
        except Exception as e:
            logger.error(f"Error activating PAIM: {str(e)}")
            return AIgencyPAIMResponse(success=False, error={"message": str(e)})
    
    async def deactivate_paim(self, paim_id: str) -> AIgencyPAIMResponse:
        """Deactivate a PAIM instance"""
        try:
            response = await self.client.post(
                f"/api/v1/paim/{paim_id}/deactivate",
                headers=self._get_auth_headers()
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    logger.info(f"Successfully deactivated PAIM: {paim_id}")
                    return AIgencyPAIMResponse(success=True, data=data)
                else:
                    return AIgencyPAIMResponse(success=False, error=data.get("error"))
            else:
                error_data = response.json() if response.content else {"message": "PAIM deactivation failed"}
                return AIgencyPAIMResponse(success=False, error=error_data)
                
        except Exception as e:
            logger.error(f"Error deactivating PAIM: {str(e)}")
            return AIgencyPAIMResponse(success=False, error={"message": str(e)})
    
    async def list_paims(self) -> AIgencyPAIMResponse:
        """List all PAIMs for the authenticated user"""
        try:
            response = await self.client.get(
                "/api/v1/paim",
                headers=self._get_auth_headers()
            )
            
            if response.status_code == 200:
                data = response.json()
                return AIgencyPAIMResponse(success=True, data=data)
            else:
                error_data = response.json() if response.content else {"message": "Failed to list PAIMs"}
                return AIgencyPAIMResponse(success=False, error=error_data)
                
        except Exception as e:
            logger.error(f"Error listing PAIMs: {str(e)}")
            return AIgencyPAIMResponse(success=False, error={"message": str(e)})

    # Agent Framework Methods

    async def execute_agent(
        self,
        agent_id: str,
        query: str,
        parameters: Dict[str, Any] = None,
        context: Dict[str, Any] = None
    ) -> AIgencyAgentResponse:
        """Execute an agent with COVE-specific parameters"""
        if parameters is None:
            parameters = {}

        if context is None:
            context = {
                "userId": self.user_id,
                "sessionId": f"cove_session_{datetime.now(timezone.utc).timestamp()}",
                "source": "cove_paim"
            }

        try:
            response = await self.client.post(
                f"/api/v1/agents/{agent_id}/execute",
                headers=self._get_auth_headers(),
                json={
                    "input": {
                        "query": query,
                        "parameters": parameters
                    },
                    "context": context
                }
            )

            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    logger.info(f"Successfully executed agent: {agent_id}")
                    return AIgencyAgentResponse(success=True, data=data)
                else:
                    return AIgencyAgentResponse(success=False, error=data.get("error"))
            else:
                error_data = response.json() if response.content else {"message": "Agent execution failed"}
                return AIgencyAgentResponse(success=False, error=error_data)

        except Exception as e:
            logger.error(f"Error executing agent: {str(e)}")
            return AIgencyAgentResponse(success=False, error={"message": str(e)})

    async def list_agents(self) -> AIgencyAgentResponse:
        """List available agents"""
        try:
            response = await self.client.get(
                "/api/v1/agents",
                headers=self._get_auth_headers()
            )

            if response.status_code == 200:
                data = response.json()
                return AIgencyAgentResponse(success=True, data=data)
            else:
                error_data = response.json() if response.content else {"message": "Failed to list agents"}
                return AIgencyAgentResponse(success=False, error=error_data)

        except Exception as e:
            logger.error(f"Error listing agents: {str(e)}")
            return AIgencyAgentResponse(success=False, error={"message": str(e)})

    # PowerOps Gamification Methods

    async def get_user_stats(self) -> Dict[str, Any]:
        """Get PowerOps user statistics"""
        try:
            response = await self.client.get(
                "/api/v1/powerops/user-stats",
                headers=self._get_auth_headers()
            )

            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    return {"success": True, "data": data.get("data")}
                else:
                    return {"success": False, "error": data.get("error")}
            else:
                error_data = response.json() if response.content else {"message": "Failed to get user stats"}
                return {"success": False, "error": error_data}

        except Exception as e:
            logger.error(f"Error getting user stats: {str(e)}")
            return {"success": False, "error": {"message": str(e)}}

    async def complete_task(
        self,
        task_id: str,
        task_type: str,
        difficulty: str = "medium",
        time_spent: int = 300
    ) -> Dict[str, Any]:
        """Complete a PowerOps task"""
        try:
            response = await self.client.post(
                "/api/v1/powerops/complete-task",
                headers=self._get_auth_headers(),
                json={
                    "taskId": task_id,
                    "taskType": task_type,
                    "difficulty": difficulty,
                    "timeSpent": time_spent
                }
            )

            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    logger.info(f"Successfully completed task: {task_id}")
                    return {"success": True, "data": data.get("data")}
                else:
                    return {"success": False, "error": data.get("error")}
            else:
                error_data = response.json() if response.content else {"message": "Task completion failed"}
                return {"success": False, "error": error_data}

        except Exception as e:
            logger.error(f"Error completing task: {str(e)}")
            return {"success": False, "error": {"message": str(e)}}

    async def get_leaderboard(self) -> Dict[str, Any]:
        """Get PowerOps leaderboard"""
        try:
            response = await self.client.get(
                "/api/v1/powerops/leaderboard",
                headers=self._get_auth_headers()
            )

            if response.status_code == 200:
                data = response.json()
                return {"success": True, "data": data.get("data")}
            else:
                error_data = response.json() if response.content else {"message": "Failed to get leaderboard"}
                return {"success": False, "error": error_data}

        except Exception as e:
            logger.error(f"Error getting leaderboard: {str(e)}")
            return {"success": False, "error": {"message": str(e)}}

    # Health Check Methods

    async def health_check(self) -> Dict[str, Any]:
        """Check The AIgency backend health"""
        try:
            response = await self.client.get("/health")

            if response.status_code == 200:
                data = response.json() if response.content else {"status": "healthy"}
                return {"success": True, "data": data}
            else:
                return {"success": False, "error": {"message": f"Health check failed: {response.status_code}"}}

        except Exception as e:
            logger.error(f"Error in health check: {str(e)}")
            return {"success": False, "error": {"message": str(e)}}

    async def api_health_check(self) -> Dict[str, Any]:
        """Check The AIgency API health"""
        try:
            response = await self.client.get("/api/v1/health")

            if response.status_code == 200:
                data = response.json() if response.content else {"status": "healthy"}
                return {"success": True, "data": data}
            else:
                return {"success": False, "error": {"message": f"API health check failed: {response.status_code}"}}

        except Exception as e:
            logger.error(f"Error in API health check: {str(e)}")
            return {"success": False, "error": {"message": str(e)}}

    async def db_health_check(self) -> Dict[str, Any]:
        """Check The AIgency database health"""
        try:
            response = await self.client.get("/health/db")

            if response.status_code == 200:
                data = response.json() if response.content else {"status": "healthy"}
                return {"success": True, "data": data}
            else:
                return {"success": False, "error": {"message": f"DB health check failed: {response.status_code}"}}

        except Exception as e:
            logger.error(f"Error in DB health check: {str(e)}")
            return {"success": False, "error": {"message": str(e)}}


# Factory function to create AIgency client
def create_aigency_client(base_url: str = "http://localhost:3000") -> AIgencyClient:
    """Create and configure AIgency client instance"""
    config = AIgencyConfig(base_url=base_url)
    return AIgencyClient(config)
