version: '3.8'

services:
  # Redis for caching and session management
  redis:
    image: redis:7-alpine
    container_name: cove-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["C<PERSON>", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - cove-network

  # COVE Integration Server
  cove-integration:
    build:
      context: ./backend/services
      dockerfile: Dockerfile
    container_name: cove-integration-server
    ports:
      - "8000:8000"
    environment:
      - COVE_HOST=0.0.0.0
      - COVE_PORT=8000
      - REDIS_URL=redis://redis:6379
      - AIGENCY_URL=http://aigency:3000
      - PYTHON_BACKEND_URL=http://python-backend:8001
      - JWT_SECRET=${JWT_SECRET:-your-secret-key-change-in-production}
      - ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080
      - LOG_LEVEL=INFO
    depends_on:
      redis:
        condition: service_healthy
    volumes:
      - ./backend/services:/app
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - cove-network
    restart: unless-stopped

  # The AIgency TypeScript Service
  aigency:
    build:
      context: ./backend/components/theaigency
      dockerfile: Dockerfile
    container_name: aigency-service
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - REDIS_URL=redis://redis:6379
      - COVE_INTEGRATION_URL=http://cove-integration:8000
    depends_on:
      redis:
        condition: service_healthy
    volumes:
      - ./backend/components/theaigency:/app
      - /app/node_modules
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - cove-network
    restart: unless-stopped

  # Python Backend Service (existing Cove backend)
  python-backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.python
    container_name: python-backend-service
    ports:
      - "8001:8001"
    environment:
      - PYTHONPATH=/app
      - REDIS_URL=redis://redis:6379
      - COVE_INTEGRATION_URL=http://cove-integration:8000
      - PORT=8001
    depends_on:
      redis:
        condition: service_healthy
    volumes:
      - ./backend:/app
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - cove-network
    restart: unless-stopped

  # Frontend UI (existing Cove UI)
  cove-ui:
    build:
      context: ./ui
      dockerfile: Dockerfile
    container_name: cove-ui
    ports:
      - "8080:80"
    environment:
      - REACT_APP_API_URL=http://localhost:8000
      - REACT_APP_AIGENCY_URL=http://localhost:3000
      - REACT_APP_WS_URL=ws://localhost:8000
    depends_on:
      - cove-integration
      - aigency
    volumes:
      - ./ui/build:/usr/share/nginx/html
    networks:
      - cove-network
    restart: unless-stopped

  # Monitoring with Prometheus (optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: cove-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    networks:
      - cove-network
    restart: unless-stopped
    profiles:
      - monitoring

  # Grafana for visualization (optional)
  grafana:
    image: grafana/grafana:latest
    container_name: cove-grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    networks:
      - cove-network
    restart: unless-stopped
    profiles:
      - monitoring

volumes:
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  cove-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
