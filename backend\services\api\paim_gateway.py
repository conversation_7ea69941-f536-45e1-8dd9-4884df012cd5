"""
PAIM API Gateway - Bridge between COVE and The AIgency
Handles routing, authentication, and multi-tenant isolation for PAIM requests
"""

import asyncio
import json
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

from fastapi import FastAP<PERSON>, HTTPException, Depends, WebSocket, WebSocketDisconnect
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import httpx
import jwt
from redis import Redis

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PAIMTier(str, Enum):
    """PAIM hierarchy tiers"""
    SYSTEM_ADMIN = "SYSTEM_ADMIN"
    COMPANY_ADMIN = "COMPANY_ADMIN"
    POWER_USER = "POWER_USER"
    PERSONAL = "PERSONAL"

class CulturalRegion(str, Enum):
    """Supported cultural regions"""
    UAE = "uae"
    SAUDI = "saudi"
    KUWAIT = "kuwait"
    QATAR = "qatar"
    BAHRAIN = "bahrain"
    OMAN = "oman"

@dataclass
class PAIMContext:
    """PAIM execution context"""
    paim_id: str
    paim_tier: PAIMTier
    tenant_id: str
    user_id: str
    cultural_region: CulturalRegion
    permissions: List[str]
    session_id: str

class PAIMRequest(BaseModel):
    """Standard PAIM request format"""
    method: str = Field(..., description="HTTP method")
    path: str = Field(..., description="Request path")
    headers: Dict[str, str] = Field(default_factory=dict)
    body: Optional[Dict[str, Any]] = None
    cultural_context: Optional[Dict[str, Any]] = None
    paim_context: Optional[Dict[str, Any]] = None

class PAIMResponse(BaseModel):
    """Standard PAIM response format"""
    success: bool
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    cultural_adaptation: Optional[Dict[str, Any]] = None

class PAIMGateway:
    """Main PAIM API Gateway class"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.app = FastAPI(title="PAIM API Gateway", version="1.0.0")
        self.security = HTTPBearer()
        self.redis_client = Redis.from_url(config.get("redis_url", "redis://localhost:6379"))
        self.aigency_base_url = config.get("aigency_url", "http://localhost:3000")
        self.python_backend_url = config.get("python_backend_url", "http://localhost:8000")
        
        # Active WebSocket connections
        self.active_connections: Dict[str, WebSocket] = {}
        
        # Setup middleware and routes
        self._setup_middleware()
        self._setup_routes()
        
    def _setup_middleware(self):
        """Setup CORS and other middleware"""
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=self.config.get("allowed_origins", ["*"]),
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
    def _setup_routes(self):
        """Setup API routes"""
        
        @self.app.get("/health")
        async def health_check():
            """Health check endpoint"""
            return {"status": "healthy", "timestamp": datetime.utcnow().isoformat()}
        
        @self.app.post("/api/v1/paim/execute")
        async def execute_paim_request(
            request: PAIMRequest,
            paim_context: PAIMContext = Depends(self._get_paim_context)
        ):
            """Execute PAIM request with proper routing"""
            try:
                # Validate permissions
                if not self._validate_permissions(paim_context, request):
                    raise HTTPException(status_code=403, detail="Insufficient permissions")
                
                # Route request based on path and PAIM tier
                response = await self._route_request(request, paim_context)
                
                # Apply cultural adaptation if needed
                if request.cultural_context:
                    response = await self._apply_cultural_adaptation(response, request.cultural_context)
                
                # Log request for audit
                await self._log_request(request, response, paim_context)
                
                return response
                
            except Exception as e:
                logger.error(f"Error executing PAIM request: {str(e)}")
                return PAIMResponse(
                    success=False,
                    error=f"Request execution failed: {str(e)}"
                )
        
        @self.app.websocket("/ws/paim/{paim_id}")
        async def websocket_endpoint(websocket: WebSocket, paim_id: str):
            """WebSocket endpoint for real-time PAIM communication"""
            await websocket.accept()
            self.active_connections[paim_id] = websocket
            
            try:
                while True:
                    data = await websocket.receive_text()
                    message = json.loads(data)
                    
                    # Process real-time message
                    response = await self._process_realtime_message(message, paim_id)
                    await websocket.send_text(json.dumps(response))
                    
            except WebSocketDisconnect:
                del self.active_connections[paim_id]
                logger.info(f"PAIM {paim_id} disconnected")
        
        @self.app.get("/api/v1/paim/status")
        async def get_paim_status(
            paim_context: PAIMContext = Depends(self._get_paim_context)
        ):
            """Get PAIM system status"""
            return {
                "paim_id": paim_context.paim_id,
                "tier": paim_context.paim_tier,
                "status": "active",
                "cultural_region": paim_context.cultural_region,
                "connected_agents": len(self.active_connections),
                "timestamp": datetime.utcnow().isoformat()
            }
    
    async def _get_paim_context(self, credentials: HTTPAuthorizationCredentials = Depends(HTTPBearer())) -> PAIMContext:
        """Extract PAIM context from JWT token"""
        try:
            token = credentials.credentials
            payload = jwt.decode(token, self.config["jwt_secret"], algorithms=["HS256"])
            
            return PAIMContext(
                paim_id=payload["paim_id"],
                paim_tier=PAIMTier(payload["paim_tier"]),
                tenant_id=payload["tenant_id"],
                user_id=payload["user_id"],
                cultural_region=CulturalRegion(payload.get("cultural_region", "uae")),
                permissions=payload.get("permissions", []),
                session_id=payload["session_id"]
            )
        except Exception as e:
            raise HTTPException(status_code=401, detail=f"Invalid token: {str(e)}")
    
    def _validate_permissions(self, context: PAIMContext, request: PAIMRequest) -> bool:
        """Validate PAIM permissions for request"""
        # System admin can access everything
        if context.paim_tier == PAIMTier.SYSTEM_ADMIN:
            return True
        
        # Define tier-based permissions
        tier_permissions = {
            PAIMTier.COMPANY_ADMIN: ["company_data", "user_management", "agent_config"],
            PAIMTier.POWER_USER: ["personal_data", "agent_execute", "cultural_adapt"],
            PAIMTier.PERSONAL: ["personal_data", "basic_agent"]
        }
        
        required_permission = self._get_required_permission(request.path)
        allowed_permissions = tier_permissions.get(context.paim_tier, [])
        
        return required_permission in allowed_permissions
    
    def _get_required_permission(self, path: str) -> str:
        """Get required permission for API path"""
        permission_map = {
            "/api/v1/agents/execute": "agent_execute",
            "/api/v1/agents/config": "agent_config",
            "/api/v1/cultural/process": "cultural_adapt",
            "/api/v1/users": "user_management",
            "/api/v1/company": "company_data",
            "/api/v1/personal": "personal_data"
        }
        
        for pattern, permission in permission_map.items():
            if path.startswith(pattern):
                return permission
        
        return "basic_agent"  # Default permission
    
    async def _route_request(self, request: PAIMRequest, context: PAIMContext) -> PAIMResponse:
        """Route request to appropriate backend service"""
        # Determine target service based on request path
        if request.path.startswith("/api/v1/agents"):
            # Route to TypeScript AIgency service
            return await self._route_to_aigency(request, context)
        elif request.path.startswith("/api/v1/cultural"):
            # Route to Python cultural service
            return await self._route_to_python_backend(request, context)
        else:
            # Default to Python backend
            return await self._route_to_python_backend(request, context)
    
    async def _route_to_aigency(self, request: PAIMRequest, context: PAIMContext) -> PAIMResponse:
        """Route request to TypeScript AIgency service"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.request(
                    method=request.method,
                    url=f"{self.aigency_base_url}{request.path}",
                    headers={
                        **request.headers,
                        "X-PAIM-ID": context.paim_id,
                        "X-PAIM-TIER": context.paim_tier.value,
                        "X-TENANT-ID": context.tenant_id,
                        "X-CULTURAL-REGION": context.cultural_region.value
                    },
                    json=request.body,
                    timeout=30.0
                )
                
                return PAIMResponse(
                    success=response.status_code < 400,
                    data=response.json() if response.content else None,
                    metadata={"service": "aigency", "status_code": response.status_code}
                )
        except Exception as e:
            logger.error(f"Error routing to AIgency: {str(e)}")
            return PAIMResponse(
                success=False,
                error=f"AIgency service error: {str(e)}"
            )
    
    async def _route_to_python_backend(self, request: PAIMRequest, context: PAIMContext) -> PAIMResponse:
        """Route request to Python backend service"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.request(
                    method=request.method,
                    url=f"{self.python_backend_url}{request.path}",
                    headers={
                        **request.headers,
                        "X-PAIM-ID": context.paim_id,
                        "X-PAIM-TIER": context.paim_tier.value,
                        "X-TENANT-ID": context.tenant_id,
                        "X-CULTURAL-REGION": context.cultural_region.value
                    },
                    json=request.body,
                    timeout=30.0
                )
                
                return PAIMResponse(
                    success=response.status_code < 400,
                    data=response.json() if response.content else None,
                    metadata={"service": "python_backend", "status_code": response.status_code}
                )
        except Exception as e:
            logger.error(f"Error routing to Python backend: {str(e)}")
            return PAIMResponse(
                success=False,
                error=f"Python backend service error: {str(e)}"
            )

    async def _apply_cultural_adaptation(self, response: PAIMResponse, cultural_context: Dict[str, Any]) -> PAIMResponse:
        """Apply cultural adaptation to response"""
        try:
            # Call cultural adaptation service
            async with httpx.AsyncClient() as client:
                adaptation_response = await client.post(
                    f"{self.python_backend_url}/api/v1/cultural/adapt",
                    json={
                        "response": response.dict(),
                        "cultural_context": cultural_context
                    },
                    timeout=10.0
                )

                if adaptation_response.status_code == 200:
                    adapted_data = adaptation_response.json()
                    response.cultural_adaptation = adapted_data.get("adaptation_metadata")
                    if adapted_data.get("adapted_content"):
                        response.data = adapted_data["adapted_content"]

        except Exception as e:
            logger.warning(f"Cultural adaptation failed: {str(e)}")
            # Continue without adaptation rather than failing

        return response

    async def _log_request(self, request: PAIMRequest, response: PAIMResponse, context: PAIMContext):
        """Log request for audit purposes"""
        try:
            audit_log = {
                "timestamp": datetime.utcnow().isoformat(),
                "paim_id": context.paim_id,
                "paim_tier": context.paim_tier.value,
                "tenant_id": context.tenant_id,
                "user_id": context.user_id,
                "method": request.method,
                "path": request.path,
                "success": response.success,
                "cultural_region": context.cultural_region.value,
                "session_id": context.session_id
            }

            # Store in Redis for real-time monitoring
            await self._store_audit_log(audit_log)

        except Exception as e:
            logger.error(f"Failed to log request: {str(e)}")

    async def _store_audit_log(self, audit_log: Dict[str, Any]):
        """Store audit log in Redis"""
        try:
            # Store with TTL of 30 days
            key = f"audit:{audit_log['paim_id']}:{audit_log['timestamp']}"
            self.redis_client.setex(key, 2592000, json.dumps(audit_log))

            # Also add to recent activity list
            recent_key = f"recent_activity:{audit_log['paim_id']}"
            self.redis_client.lpush(recent_key, json.dumps(audit_log))
            self.redis_client.ltrim(recent_key, 0, 99)  # Keep last 100 activities

        except Exception as e:
            logger.error(f"Failed to store audit log: {str(e)}")

    async def _process_realtime_message(self, message: Dict[str, Any], paim_id: str) -> Dict[str, Any]:
        """Process real-time WebSocket message"""
        try:
            message_type = message.get("type")

            if message_type == "ping":
                return {"type": "pong", "timestamp": datetime.utcnow().isoformat()}

            elif message_type == "agent_status_request":
                # Get agent status from AIgency
                async with httpx.AsyncClient() as client:
                    response = await client.get(
                        f"{self.aigency_base_url}/api/v1/agents/health/all",
                        headers={"X-PAIM-ID": paim_id},
                        timeout=10.0
                    )

                    if response.status_code == 200:
                        return {
                            "type": "agent_status_response",
                            "data": response.json(),
                            "timestamp": datetime.utcnow().isoformat()
                        }

            elif message_type == "cultural_adaptation_update":
                # Process cultural learning update
                return await self._process_cultural_update(message.get("data", {}), paim_id)

            return {"type": "error", "message": f"Unknown message type: {message_type}"}

        except Exception as e:
            logger.error(f"Error processing real-time message: {str(e)}")
            return {"type": "error", "message": str(e)}

    async def _process_cultural_update(self, data: Dict[str, Any], paim_id: str) -> Dict[str, Any]:
        """Process cultural adaptation learning update"""
        try:
            # Send to cultural learning service
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.python_backend_url}/api/v1/cultural/learn",
                    json={"paim_id": paim_id, "learning_data": data},
                    timeout=15.0
                )

                if response.status_code == 200:
                    return {
                        "type": "cultural_update_response",
                        "success": True,
                        "data": response.json(),
                        "timestamp": datetime.utcnow().isoformat()
                    }
                else:
                    return {
                        "type": "cultural_update_response",
                        "success": False,
                        "error": f"Cultural service error: {response.status_code}"
                    }

        except Exception as e:
            logger.error(f"Error processing cultural update: {str(e)}")
            return {
                "type": "cultural_update_response",
                "success": False,
                "error": str(e)
            }

    async def broadcast_to_paim_tier(self, tier: PAIMTier, message: Dict[str, Any]):
        """Broadcast message to all PAIMs of a specific tier"""
        # This would require storing PAIM tier information with connections
        # For now, we'll implement a basic version
        for paim_id, websocket in self.active_connections.items():
            try:
                await websocket.send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"Failed to broadcast to {paim_id}: {str(e)}")

    def get_app(self) -> FastAPI:
        """Get the FastAPI application instance"""
        return self.app


# Factory function to create gateway instance
def create_paim_gateway(config: Dict[str, Any]) -> PAIMGateway:
    """Create and configure PAIM Gateway instance"""
    return PAIMGateway(config)


# Default configuration
DEFAULT_CONFIG = {
    "jwt_secret": "your-secret-key-here",
    "redis_url": "redis://localhost:6379",
    "aigency_url": "http://localhost:3000",
    "python_backend_url": "http://localhost:8000",
    "allowed_origins": ["http://localhost:3000", "http://localhost:8080"],
    "log_level": "INFO"
}
